#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
简化的离线图表测试脚本
"""

import os
import json
import sys
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def load_embedded_resources():
    """加载嵌入式JavaScript资源"""
    resources = {}
    
    try:
        js_dir = project_root / "src" / "resources" / "js"
        
        # 加载ECharts主库
        echarts_file = js_dir / "echarts.min.js"
        if echarts_file.exists():
            with open(echarts_file, 'r', encoding='utf-8') as f:
                resources['echarts'] = f.read()
            print(f"✅ ECharts主库已加载: {len(resources['echarts'])} 字符")
        else:
            print(f"❌ ECharts主库不存在: {echarts_file}")
            return None
            
        # 加载词云插件
        wordcloud_file = js_dir / "echarts-wordcloud.min.js"
        if wordcloud_file.exists():
            with open(wordcloud_file, 'r', encoding='utf-8') as f:
                resources['wordcloud'] = f.read()
            print(f"✅ 词云插件已加载: {len(resources['wordcloud'])} 字符")
        else:
            print(f"❌ 词云插件不存在: {wordcloud_file}")
            
    except Exception as e:
        print(f"❌ 加载资源失败: {e}")
        return None
        
    return resources

def generate_offline_chart_html(resources):
    """生成完全离线的图表HTML"""
    
    if not resources or 'echarts' not in resources:
        return None
    
    # 测试数据
    test_data = {
        'title': '离线测试图表',
        'x_data': ['A', 'B', 'C', 'D'],
        'y_data': [10, 20, 15, 25]
    }
    
    # 图表配置
    chart_option = {
        'title': {'text': test_data['title']},
        'tooltip': {},
        'xAxis': {'data': test_data['x_data']},
        'yAxis': {},
        'series': [{
            'name': '测试数据',
            'type': 'bar',
            'data': test_data['y_data']
        }]
    }
    
    # 生成HTML
    html = f"""<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{test_data['title']}</title>
    <style>
        body {{
            margin: 0;
            padding: 20px;
            background: white;
            font-family: Arial, sans-serif;
        }}
        #chart-container {{
            width: 800px;
            height: 600px;
            margin: 0 auto;
            border: 1px solid #ddd;
        }}
        .info {{
            text-align: center;
            margin: 20px 0;
            color: green;
            font-weight: bold;
        }}
    </style>
</head>
<body>
    <div class="info">✅ 完全离线图表测试 - 无网络依赖</div>
    <div id="chart-container"></div>
    
    <!-- 嵌入式ECharts资源 - 完全离线 -->
    <script type="text/javascript">
    {resources['echarts']}
    </script>
    
    <script type="text/javascript">
    // 初始化图表
    console.log('开始初始化ECharts图表...');
    var chartDom = document.getElementById('chart-container');
    var myChart = echarts.init(chartDom);
    
    // 图表配置
    var option = {json.dumps(chart_option, ensure_ascii=False)};
    
    // 设置图表
    myChart.setOption(option);
    console.log('图表设置完成');
    
    // 响应式调整
    window.addEventListener('resize', function() {{
        myChart.resize();
    }});
    
    // 显示成功信息
    console.log('✅ 离线图表渲染成功！');
    </script>
</body>
</html>"""
    
    return html

def main():
    """主函数"""
    print("🧪 离线图表测试")
    print("="*50)
    
    # 1. 加载嵌入式资源
    print("1. 加载嵌入式JavaScript资源...")
    resources = load_embedded_resources()
    
    if not resources:
        print("❌ 资源加载失败，无法继续测试")
        return
    
    # 2. 生成离线图表HTML
    print("\n2. 生成离线图表HTML...")
    html = generate_offline_chart_html(resources)
    
    if not html:
        print("❌ HTML生成失败")
        return
    
    print(f"✅ HTML生成成功: {len(html)} 字符")
    
    # 3. 检查HTML内容
    print("\n3. 检查HTML内容...")
    if 'https://' in html or 'http://' in html:
        print("⚠️ 警告: HTML中包含在线资源链接")
        # 查找具体的链接
        import re
        links = re.findall(r'https?://[^\s"\'<>]+', html)
        for link in links:
            print(f"   发现链接: {link}")
    else:
        print("✅ HTML完全离线，无外部资源依赖")
    
    # 4. 保存测试文件
    print("\n4. 保存测试文件...")
    test_file = project_root / "test_offline_chart_simple.html"
    
    try:
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write(html)
        print(f"✅ 测试文件已保存: {test_file}")
        print(f"💡 请在浏览器中打开此文件测试离线图表功能")
    except Exception as e:
        print(f"❌ 保存文件失败: {e}")
    
    # 5. 验证嵌入式资源
    print("\n5. 验证嵌入式资源...")
    if 'echarts' in resources and len(resources['echarts']) > 1000000:
        print("✅ ECharts主库资源完整")
    else:
        print("❌ ECharts主库资源不完整")
    
    if 'wordcloud' in resources and len(resources['wordcloud']) > 10000:
        print("✅ 词云插件资源完整")
    else:
        print("❌ 词云插件资源不完整")
    
    print("\n" + "="*50)
    print("测试完成")

if __name__ == "__main__":
    main()
