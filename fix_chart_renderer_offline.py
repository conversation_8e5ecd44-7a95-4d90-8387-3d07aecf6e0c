#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
直接修复图表渲染器的离线问题

这个脚本会直接修改现有的图表渲染器，使其真正使用嵌入式资源
而不是依赖PyEcharts的标准渲染流程。

使用方法：
1. 运行此脚本
2. 重启应用程序
3. 测试图表功能

作者: AI Assistant
创建时间: 2025-08-05
"""

import os
import sys
import shutil
import json
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def load_embedded_resources():
    """加载嵌入式JavaScript资源"""
    resources = {}
    
    try:
        js_dir = project_root / "src" / "resources" / "js"
        
        # 加载ECharts主库
        echarts_file = js_dir / "echarts.min.js"
        if echarts_file.exists():
            with open(echarts_file, 'r', encoding='utf-8') as f:
                resources['echarts'] = f.read()
            print(f"✅ ECharts主库已加载: {len(resources['echarts'])} 字符")
        else:
            print(f"❌ ECharts主库不存在: {echarts_file}")
            return None
            
        # 加载词云插件
        wordcloud_file = js_dir / "echarts-wordcloud.min.js"
        if wordcloud_file.exists():
            with open(wordcloud_file, 'r', encoding='utf-8') as f:
                resources['wordcloud'] = f.read()
            print(f"✅ 词云插件已加载: {len(resources['wordcloud'])} 字符")
        else:
            print(f"❌ 词云插件不存在: {wordcloud_file}")
            
    except Exception as e:
        print(f"❌ 加载资源失败: {e}")
        return None
        
    return resources

def create_true_offline_renderer():
    """创建真正的离线渲染器"""
    
    resources = load_embedded_resources()
    if not resources:
        return False
    
    # 创建新的离线渲染器文件
    offline_renderer_code = f'''#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
真正的离线图表渲染器

这个模块完全绕过PyEcharts的标准渲染流程，
直接生成包含嵌入式JavaScript的HTML。

自动生成时间: 2025-08-05
"""

import json
import pandas as pd
from typing import Dict, Any, Optional

class TrueOfflineChartRenderer:
    """真正的离线图表渲染器"""
    
    def __init__(self):
        # 嵌入式JavaScript资源
        self.echarts_js = """{resources['echarts']}"""
        
        self.wordcloud_js = """{resources['wordcloud']}"""
    
    def render_bar_chart(self, config: Dict[str, Any], data: pd.DataFrame) -> str:
        """渲染柱状图 - 完全离线版本"""
        try:
            # 提取配置
            title = config.get('title', '柱状图')
            width = config.get('width', 800)
            height = config.get('height', 600)
            x_field = config.get('x_field')
            y_field = config.get('y_field')
            
            # 处理数据
            if data.empty:
                return self._get_no_data_html()
            
            # 自动检测字段
            if not x_field and len(data.columns) > 0:
                x_field = data.columns[0]
            if not y_field and len(data.columns) > 1:
                y_field = data.columns[1]
            elif not y_field:
                y_field = x_field
            
            # 提取数据
            x_data = data[x_field].astype(str).tolist()
            y_data = data[y_field].fillna(0).astype(float).tolist()
            
            return self._generate_chart_html('bar', title, width, height, x_data, y_data, y_field)
            
        except Exception as e:
            return self._get_error_html(f"柱状图渲染失败: {{e}}")
    
    def render_line_chart(self, config: Dict[str, Any], data: pd.DataFrame) -> str:
        """渲染折线图 - 完全离线版本"""
        try:
            title = config.get('title', '折线图')
            width = config.get('width', 800)
            height = config.get('height', 600)
            x_field = config.get('x_field')
            y_field = config.get('y_field')
            
            if data.empty:
                return self._get_no_data_html()
            
            if not x_field and len(data.columns) > 0:
                x_field = data.columns[0]
            if not y_field and len(data.columns) > 1:
                y_field = data.columns[1]
            elif not y_field:
                y_field = x_field
            
            x_data = data[x_field].astype(str).tolist()
            y_data = data[y_field].fillna(0).astype(float).tolist()
            
            return self._generate_chart_html('line', title, width, height, x_data, y_data, y_field)
            
        except Exception as e:
            return self._get_error_html(f"折线图渲染失败: {{e}}")
    
    def render_pie_chart(self, config: Dict[str, Any], data: pd.DataFrame) -> str:
        """渲染饼图 - 完全离线版本"""
        try:
            title = config.get('title', '饼图')
            width = config.get('width', 800)
            height = config.get('height', 600)
            name_field = config.get('name_field')
            value_field = config.get('value_field')
            
            if data.empty:
                return self._get_no_data_html()
            
            if not name_field and len(data.columns) > 0:
                name_field = data.columns[0]
            if not value_field and len(data.columns) > 1:
                value_field = data.columns[1]
            elif not value_field:
                value_field = name_field
            
            # 饼图数据格式不同
            pie_data = []
            for _, row in data.iterrows():
                pie_data.append({{
                    'name': str(row[name_field]),
                    'value': float(row[value_field]) if row[value_field] is not None else 0
                }})
            
            return self._generate_pie_chart_html(title, width, height, pie_data)
            
        except Exception as e:
            return self._get_error_html(f"饼图渲染失败: {{e}}")
    
    def _generate_chart_html(self, chart_type: str, title: str, width: int, height: int, 
                           x_data: list, y_data: list, series_name: str) -> str:
        """生成图表HTML"""
        
        chart_option = {{
            'title': {{'text': title}},
            'tooltip': {{}},
            'xAxis': {{'data': x_data}},
            'yAxis': {{}},
            'series': [{{
                'name': series_name,
                'type': chart_type,
                'data': y_data
            }}]
        }}
        
        return self._generate_html_template(title, width, height, chart_option)
    
    def _generate_pie_chart_html(self, title: str, width: int, height: int, pie_data: list) -> str:
        """生成饼图HTML"""
        
        chart_option = {{
            'title': {{'text': title}},
            'tooltip': {{'trigger': 'item'}},
            'series': [{{
                'name': title,
                'type': 'pie',
                'radius': '50%',
                'data': pie_data
            }}]
        }}
        
        return self._generate_html_template(title, width, height, chart_option)
    
    def _generate_html_template(self, title: str, width: int, height: int, chart_option: dict) -> str:
        """生成HTML模板"""
        
        chart_id = f"chart_{{hash(str(chart_option)) % 10000}}"
        
        html = f"""<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{title}}</title>
    <style>
        body {{
            margin: 0;
            padding: 20px;
            background: white;
            font-family: Arial, sans-serif;
        }}
        .header {{
            text-align: center;
            margin-bottom: 20px;
            color: green;
            font-weight: bold;
        }}
        #{{chart_id}} {{
            width: {{width}}px;
            height: {{height}}px;
            margin: 0 auto;
            border: 1px solid #ddd;
        }}
    </style>
</head>
<body>
    <div class="header">✅ 完全离线图表 - 无网络依赖</div>
    <div id="{{chart_id}}"></div>
    
    <!-- 嵌入式ECharts资源 - 完全离线 -->
    <script type="text/javascript">
    {{self.echarts_js}}
    </script>
    
    <script type="text/javascript">
    // 初始化图表
    console.log('开始初始化离线ECharts图表...');
    var chartDom = document.getElementById('{{chart_id}}');
    var myChart = echarts.init(chartDom);
    
    // 图表配置
    var option = {{json.dumps(chart_option, ensure_ascii=False)}};
    
    // 设置图表
    myChart.setOption(option);
    console.log('✅ 离线图表渲染成功！');
    
    // 响应式调整
    window.addEventListener('resize', function() {{
        myChart.resize();
    }});
    </script>
</body>
</html>"""
        
        return html
    
    def _get_no_data_html(self) -> str:
        """生成无数据HTML"""
        return """
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>无数据</title>
</head>
<body>
    <div style="padding: 20px; text-align: center; color: #999;">
        <h3>暂无数据</h3>
        <p>没有可显示的图表数据</p>
    </div>
</body>
</html>
"""
    
    def _get_error_html(self, error_msg: str) -> str:
        """生成错误HTML"""
        return f"""
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>图表渲染错误</title>
</head>
<body>
    <div style="padding: 20px; text-align: center; color: red;">
        <h3>图表渲染失败</h3>
        <p>{{error_msg}}</p>
    </div>
</body>
</html>
"""

# 创建全局实例
true_offline_renderer = TrueOfflineChartRenderer()

def render_chart_offline(chart_type: str, config: Dict[str, Any], data: pd.DataFrame) -> str:
    """统一的离线图表渲染接口"""
    
    if chart_type == 'bar':
        return true_offline_renderer.render_bar_chart(config, data)
    elif chart_type == 'line':
        return true_offline_renderer.render_line_chart(config, data)
    elif chart_type == 'pie':
        return true_offline_renderer.render_pie_chart(config, data)
    else:
        return true_offline_renderer._get_error_html(f"不支持的图表类型: {{chart_type}}")
'''
    
    # 保存离线渲染器文件
    offline_renderer_file = project_root / "src" / "utils" / "true_offline_renderer.py"
    
    try:
        with open(offline_renderer_file, 'w', encoding='utf-8') as f:
            f.write(offline_renderer_code)
        print(f"✅ 真正的离线渲染器已创建: {offline_renderer_file}")
        return True
    except Exception as e:
        print(f"❌ 创建离线渲染器失败: {e}")
        return False

def patch_chart_renderer():
    """修补现有的图表渲染器"""
    
    chart_renderer_file = project_root / "src" / "utils" / "chart_renderer.py"
    backup_file = project_root / "src" / "utils" / "chart_renderer_backup.py"
    
    if not chart_renderer_file.exists():
        print(f"❌ 图表渲染器文件不存在: {chart_renderer_file}")
        return False
    
    # 创建备份
    try:
        shutil.copy2(chart_renderer_file, backup_file)
        print(f"✅ 已创建备份: {backup_file}")
    except Exception as e:
        print(f"❌ 创建备份失败: {e}")
        return False
    
    # 读取原始文件
    try:
        with open(chart_renderer_file, 'r', encoding='utf-8') as f:
            original_content = f.read()
    except Exception as e:
        print(f"❌ 读取原始文件失败: {e}")
        return False
    
    # 在文件开头添加导入
    import_patch = '''
# 🔧 离线修复补丁 - 导入真正的离线渲染器
try:
    from .true_offline_renderer import render_chart_offline
    OFFLINE_RENDERER_AVAILABLE = True
    print("✅ 真正的离线渲染器已加载")
except ImportError:
    OFFLINE_RENDERER_AVAILABLE = False
    print("❌ 真正的离线渲染器不可用")

'''
    
    # 查找render_chart方法并添加离线检查
    method_patch = '''
        # 🔧 离线修复补丁 - 优先使用真正的离线渲染器
        if OFFLINE_RENDERER_AVAILABLE and self.embedded_mode_enabled:
            try:
                chart_type = config.get('chart_type', 'bar')
                offline_html = render_chart_offline(chart_type, config, data)
                if offline_html and len(offline_html) > 1000:
                    self.logger.info(f"✅ 使用真正的离线渲染器成功: {chart_type}")
                    return offline_html
                else:
                    self.logger.warning("离线渲染器返回内容不足，回退到标准方法")
            except Exception as e:
                self.logger.error(f"离线渲染器失败: {e}，回退到标准方法")
        
        # 原始渲染逻辑继续...
'''
    
    # 应用补丁
    try:
        # 添加导入
        if 'from .true_offline_renderer import' not in original_content:
            original_content = import_patch + original_content
        
        # 查找render_chart方法
        if 'def render_chart(' in original_content:
            # 在方法开始处添加离线检查
            original_content = original_content.replace(
                'def render_chart(self, config: Dict[str, Any], data: pd.DataFrame) -> str:',
                f'def render_chart(self, config: Dict[str, Any], data: pd.DataFrame) -> str:{method_patch}'
            )
        
        # 写入修改后的文件
        with open(chart_renderer_file, 'w', encoding='utf-8') as f:
            f.write(original_content)
        
        print("✅ 图表渲染器补丁应用成功")
        return True
        
    except Exception as e:
        print(f"❌ 应用补丁失败: {e}")
        # 恢复备份
        try:
            shutil.copy2(backup_file, chart_renderer_file)
            print("已恢复原始文件")
        except:
            pass
        return False

def main():
    """主函数"""
    print("🔧 图表渲染器离线修复工具")
    print("="*50)
    
    # 1. 创建真正的离线渲染器
    print("1. 创建真正的离线渲染器...")
    renderer_created = create_true_offline_renderer()
    
    if not renderer_created:
        print("❌ 离线渲染器创建失败，无法继续")
        return
    
    # 2. 修补现有的图表渲染器
    print("\\n2. 修补现有的图表渲染器...")
    patch_applied = patch_chart_renderer()
    
    # 3. 总结
    print("\\n" + "="*50)
    print("📊 修复结果:")
    print(f"   离线渲染器: {'✅ 已创建' if renderer_created else '❌ 创建失败'}")
    print(f"   渲染器补丁: {'✅ 已应用' if patch_applied else '❌ 应用失败'}")
    
    if renderer_created and patch_applied:
        print("\\n🎉 修复完成！")
        print("💡 请重启应用程序，然后测试图表功能")
        print("💡 如果仍有问题，请检查日志中的离线渲染器状态")
    else:
        print("\\n❌ 修复失败，请检查错误信息")

if __name__ == "__main__":
    main()
