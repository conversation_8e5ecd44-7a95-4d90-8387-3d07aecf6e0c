#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
真实离线部署修复方案

基于实际测试失败的分析，提供可行的离线部署修复方案。

问题分析：
1. 嵌入式资源模块存在但未被实际使用
2. PyEcharts仍使用标准渲染流程，依赖外部资源
3. WebEngine网络服务崩溃，表明离线配置不完整

解决方案：
1. 修改图表渲染器，真正使用嵌入式资源
2. 完全重写HTML生成流程
3. 强化WebEngine离线配置

作者: AI Assistant
创建时间: 2025-08-05
"""

import os
import sys
import json
import logging
from pathlib import Path
from typing import Dict, Any, Optional

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

logger = logging.getLogger(__name__)


class TrueOfflineChartRenderer:
    """真正的离线图表渲染器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.embedded_resources = self._load_embedded_resources()
        
    def _load_embedded_resources(self) -> Dict[str, str]:
        """加载嵌入式JavaScript资源"""
        resources = {}
        
        try:
            js_dir = project_root / "src" / "resources" / "js"
            
            # 加载ECharts主库
            echarts_file = js_dir / "echarts.min.js"
            if echarts_file.exists():
                with open(echarts_file, 'r', encoding='utf-8') as f:
                    resources['echarts'] = f.read()
                self.logger.info(f"ECharts主库已加载: {len(resources['echarts'])} 字符")
            else:
                self.logger.error(f"ECharts主库不存在: {echarts_file}")
                
            # 加载词云插件
            wordcloud_file = js_dir / "echarts-wordcloud.min.js"
            if wordcloud_file.exists():
                with open(wordcloud_file, 'r', encoding='utf-8') as f:
                    resources['wordcloud'] = f.read()
                self.logger.info(f"词云插件已加载: {len(resources['wordcloud'])} 字符")
            else:
                self.logger.error(f"词云插件不存在: {wordcloud_file}")
                
        except Exception as e:
            self.logger.error(f"加载嵌入式资源失败: {e}")
            
        return resources
    
    def render_bar_chart_offline(self, config: Dict[str, Any], data: Any) -> str:
        """渲染柱状图 - 完全离线版本"""
        try:
            # 提取配置
            title = config.get('title', '柱状图')
            width = config.get('width', 800)
            height = config.get('height', 600)
            
            # 处理数据
            if hasattr(data, 'to_dict'):
                data_dict = data.to_dict('records')
            else:
                data_dict = data
                
            # 提取X轴和Y轴数据
            x_data = []
            y_data = []
            
            if data_dict:
                first_row = data_dict[0]
                x_field = list(first_row.keys())[0]
                y_field = list(first_row.keys())[1] if len(first_row.keys()) > 1 else x_field
                
                for row in data_dict:
                    x_data.append(str(row.get(x_field, '')))
                    y_data.append(float(row.get(y_field, 0)))
            
            # 生成完全离线的HTML
            html = self._generate_offline_html(
                chart_type='bar',
                title=title,
                width=width,
                height=height,
                x_data=x_data,
                y_data=y_data
            )
            
            return html
            
        except Exception as e:
            self.logger.error(f"离线柱状图渲染失败: {e}")
            return self._get_error_html(str(e))
    
    def _generate_offline_html(self, chart_type: str, title: str, width: int, height: int, 
                              x_data: list, y_data: list) -> str:
        """生成完全离线的HTML"""
        
        # 检查嵌入式资源
        if 'echarts' not in self.embedded_resources:
            return self._get_error_html("ECharts资源未加载")
        
        # 生成图表配置
        chart_option = {
            'title': {'text': title},
            'tooltip': {},
            'xAxis': {'data': x_data},
            'yAxis': {},
            'series': [{
                'name': '数据',
                'type': chart_type,
                'data': y_data
            }]
        }
        
        # 生成完全嵌入式HTML
        html = f"""
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{title}</title>
    <style>
        body {{
            margin: 0;
            padding: 0;
            background: white;
        }}
        #chart-container {{
            width: {width}px;
            height: {height}px;
            margin: 0 auto;
        }}
    </style>
</head>
<body>
    <div id="chart-container"></div>
    
    <!-- 嵌入式ECharts资源 - 完全离线 -->
    <script type="text/javascript">
    {self.embedded_resources['echarts']}
    </script>
    
    <script type="text/javascript">
    // 初始化图表
    var chartDom = document.getElementById('chart-container');
    var myChart = echarts.init(chartDom);
    
    // 图表配置
    var option = {json.dumps(chart_option, ensure_ascii=False)};
    
    // 设置图表
    myChart.setOption(option);
    
    // 响应式调整
    window.addEventListener('resize', function() {{
        myChart.resize();
    }});
    </script>
</body>
</html>
"""
        
        return html
    
    def _get_error_html(self, error_msg: str) -> str:
        """生成错误HTML"""
        return f"""
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>图表渲染错误</title>
</head>
<body>
    <div style="padding: 20px; text-align: center; color: red;">
        <h3>图表渲染失败</h3>
        <p>{error_msg}</p>
    </div>
</body>
</html>
"""


class WebEngineOfflineFixer:
    """WebEngine离线修复器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def apply_complete_offline_config(self):
        """应用完整的离线配置"""
        try:
            # 设置环境变量 - 更彻底的网络禁用
            offline_env_vars = {
                # 核心网络禁用
                "QTWEBENGINE_DISABLE_NETWORK": "1",
                "QTWEBENGINE_DISABLE_BACKGROUND_NETWORKING": "1",
                "QTWEBENGINE_DISABLE_SYNC": "1",
                
                # GPU和硬件加速禁用
                "QTWEBENGINE_DISABLE_GPU": "1",
                "QTWEBENGINE_DISABLE_SOFTWARE_RASTERIZER": "1",
                "QTWEBENGINE_DISABLE_ACCELERATED_2D_CANVAS": "1",
                "QTWEBENGINE_DISABLE_WEBGL": "1",
                
                # 扩展和插件禁用
                "QTWEBENGINE_DISABLE_EXTENSIONS": "1",
                "QTWEBENGINE_DISABLE_PLUGINS": "1",
                
                # 新增：更彻底的网络禁用
                "QTWEBENGINE_CHROMIUM_FLAGS": "--no-sandbox --disable-web-security --disable-features=VizDisplayCompositor --disable-gpu --disable-software-rasterizer --disable-background-networking --disable-network-service --disable-features=NetworkService --disable-background-timer-throttling --disable-backgrounding-occluded-windows --disable-renderer-backgrounding --disable-extensions --disable-sync --disable-default-apps --disable-background-mode",
                
                # 强制离线模式
                "QTWEBENGINE_OFFLINE_MODE": "1",
                "QTWEBENGINE_FORCE_LOCAL_RESOURCES": "1",
            }
            
            # 应用环境变量
            for key, value in offline_env_vars.items():
                os.environ[key] = value
                self.logger.info(f"设置环境变量: {key}={value}")
            
            self.logger.info("✅ 完整离线配置已应用")
            return True
            
        except Exception as e:
            self.logger.error(f"应用离线配置失败: {e}")
            return False


def main():
    """主修复函数"""
    print("🚨 真实离线部署修复方案")
    print("="*60)
    
    # 1. 应用WebEngine离线配置
    print("1. 应用WebEngine完整离线配置...")
    fixer = WebEngineOfflineFixer()
    if fixer.apply_complete_offline_config():
        print("✅ WebEngine离线配置成功")
    else:
        print("❌ WebEngine离线配置失败")
    
    # 2. 测试真正的离线图表渲染
    print("\n2. 测试真正的离线图表渲染...")
    renderer = TrueOfflineChartRenderer()
    
    # 创建测试数据
    test_data = [
        {'category': 'A', 'value': 10},
        {'category': 'B', 'value': 20},
        {'category': 'C', 'value': 15}
    ]
    
    test_config = {
        'title': '离线测试图表',
        'width': 600,
        'height': 400
    }
    
    # 渲染图表
    html_result = renderer.render_bar_chart_offline(test_config, test_data)
    
    if html_result and len(html_result) > 1000:
        print(f"✅ 离线图表渲染成功 ({len(html_result)} 字符)")
        
        # 保存测试文件
        test_file = project_root / "test_true_offline_chart.html"
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write(html_result)
        print(f"📄 测试文件已保存: {test_file}")
        
        # 检查是否包含在线资源链接
        if 'https://' in html_result or 'http://' in html_result:
            print("⚠️ 警告: HTML中仍包含在线资源链接")
        else:
            print("✅ HTML完全离线，无外部资源依赖")
    else:
        print("❌ 离线图表渲染失败")
    
    print("\n" + "="*60)
    print("修复方案执行完成")


if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    main()
