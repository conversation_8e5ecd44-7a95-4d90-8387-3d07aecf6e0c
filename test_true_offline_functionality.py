#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
真正的离线功能测试脚本
验证生成的HTML是否真正可以在离线环境下工作
"""

import os
import json
import sys
import re
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def analyze_html_network_dependencies(html_content):
    """分析HTML中的网络依赖"""
    
    # 真正会导致网络请求的模式
    network_patterns = [
        # 外部资源引用
        r'<script[^>]+src=["\']https?://[^"\']+["\']',
        r'<link[^>]+href=["\']https?://[^"\']+["\']',
        r'<img[^>]+src=["\']https?://[^"\']+["\']',
        r'<iframe[^>]+src=["\']https?://[^"\']+["\']',
        
        # CSS中的外部引用
        r'@import\s+["\']https?://[^"\']+["\']',
        r'url\(["\']?https?://[^"\')\s]+["\']?\)',
        
        # JavaScript中的动态加载
        r'fetch\s*\(["\']https?://[^"\']+["\']',
        r'XMLHttpRequest.*open.*["\']https?://[^"\']+["\']',
        r'\.load\s*\(["\']https?://[^"\']+["\']',
    ]
    
    # 不会导致网络请求的模式（排除）
    safe_patterns = [
        # XML命名空间
        r'http://www\.w3\.org/',
        # 许可证链接（注释中）
        r'//.*http[s]?://',
        r'/\*.*http[s]?://.*\*/',
    ]
    
    network_dependencies = []
    
    for pattern in network_patterns:
        matches = re.findall(pattern, html_content, re.IGNORECASE)
        for match in matches:
            # 检查是否是安全模式（不会触发网络请求）
            is_safe = False
            for safe_pattern in safe_patterns:
                if re.search(safe_pattern, match, re.IGNORECASE):
                    is_safe = True
                    break
            
            if not is_safe:
                network_dependencies.append(match)
    
    return network_dependencies

def test_html_in_browser_simulation():
    """模拟浏览器环境测试HTML"""
    
    test_file = project_root / "test_offline_chart_simple.html"
    
    if not test_file.exists():
        print("❌ 测试文件不存在，请先运行 test_offline_chart.py")
        return False
    
    print("📄 读取测试HTML文件...")
    with open(test_file, 'r', encoding='utf-8') as f:
        html_content = f.read()
    
    print(f"✅ HTML文件大小: {len(html_content):,} 字符")
    
    # 分析网络依赖
    print("\n🔍 分析真正的网络依赖...")
    network_deps = analyze_html_network_dependencies(html_content)
    
    if network_deps:
        print("❌ 发现真正的网络依赖:")
        for dep in network_deps:
            print(f"   {dep}")
        return False
    else:
        print("✅ 无真正的网络依赖")
    
    # 检查关键组件
    print("\n🔧 检查关键组件...")
    
    # 检查是否包含完整的ECharts代码
    if 'echarts.init' in html_content:
        print("✅ 包含ECharts初始化代码")
    else:
        print("❌ 缺少ECharts初始化代码")
        return False
    
    # 检查是否包含图表配置
    if '"type":"bar"' in html_content or '"type": "bar"' in html_content:
        print("✅ 包含图表配置")
    else:
        print("❌ 缺少图表配置")
        return False
    
    # 检查是否包含数据
    if '"data":[' in html_content or '"data": [' in html_content:
        print("✅ 包含图表数据")
    else:
        print("❌ 缺少图表数据")
        return False
    
    return True

def create_offline_test_environment():
    """创建离线测试环境"""
    
    print("\n🌐 创建离线测试环境...")
    
    # 创建一个包含网络阻断检测的HTML文件
    test_html = """<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>离线功能验证测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        #chart-container { width: 800px; height: 400px; border: 1px solid #ddd; margin: 20px 0; }
    </style>
</head>
<body>
    <h1>🧪 离线功能验证测试</h1>
    
    <div id="network-status" class="status warning">
        🔄 正在检测网络状态...
    </div>
    
    <div id="echarts-status" class="status warning">
        🔄 正在检测ECharts加载状态...
    </div>
    
    <div id="chart-status" class="status warning">
        🔄 正在检测图表渲染状态...
    </div>
    
    <div id="chart-container"></div>
    
    <script>
        // 网络状态检测
        function checkNetworkStatus() {
            const statusDiv = document.getElementById('network-status');
            
            if (navigator.onLine) {
                statusDiv.className = 'status warning';
                statusDiv.innerHTML = '⚠️ 网络连接可用 - 这不是真正的离线测试';
            } else {
                statusDiv.className = 'status success';
                statusDiv.innerHTML = '✅ 网络连接不可用 - 真正的离线环境';
            }
        }
        
        // ECharts加载检测
        function checkEChartsStatus() {
            const statusDiv = document.getElementById('echarts-status');
            
            if (typeof echarts !== 'undefined') {
                statusDiv.className = 'status success';
                statusDiv.innerHTML = '✅ ECharts已成功加载（离线模式）';
                return true;
            } else {
                statusDiv.className = 'status error';
                statusDiv.innerHTML = '❌ ECharts加载失败';
                return false;
            }
        }
        
        // 图表渲染测试
        function testChartRendering() {
            const statusDiv = document.getElementById('chart-status');
            
            try {
                if (!checkEChartsStatus()) {
                    return;
                }
                
                const chartDom = document.getElementById('chart-container');
                const myChart = echarts.init(chartDom);
                
                const option = {
                    title: { text: '离线测试图表' },
                    tooltip: {},
                    xAxis: { data: ['测试A', '测试B', '测试C'] },
                    yAxis: {},
                    series: [{
                        name: '测试数据',
                        type: 'bar',
                        data: [10, 20, 15]
                    }]
                };
                
                myChart.setOption(option);
                
                statusDiv.className = 'status success';
                statusDiv.innerHTML = '✅ 图表渲染成功（完全离线）';
                
            } catch (error) {
                statusDiv.className = 'status error';
                statusDiv.innerHTML = '❌ 图表渲染失败: ' + error.message;
            }
        }
        
        // 页面加载完成后执行测试
        window.addEventListener('load', function() {
            checkNetworkStatus();
            
            // 延迟执行图表测试，确保所有资源加载完成
            setTimeout(testChartRendering, 1000);
        });
        
        // 监听网络状态变化
        window.addEventListener('online', checkNetworkStatus);
        window.addEventListener('offline', checkNetworkStatus);
    </script>
    
    <!-- 这里会插入嵌入式ECharts代码 -->
    <script>
        // 占位符：这里应该插入完整的ECharts代码
        console.log('ECharts代码应该在这里...');
    </script>
</body>
</html>"""
    
    # 读取实际的ECharts代码
    js_dir = project_root / "src" / "resources" / "js"
    echarts_file = js_dir / "echarts.min.js"
    
    if echarts_file.exists():
        with open(echarts_file, 'r', encoding='utf-8') as f:
            echarts_code = f.read()
        
        # 替换占位符
        test_html = test_html.replace(
            "console.log('ECharts代码应该在这里...');",
            echarts_code
        )
        
        # 保存测试文件
        test_file = project_root / "offline_functionality_test.html"
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write(test_html)
        
        print(f"✅ 离线测试文件已创建: {test_file}")
        print("💡 请在浏览器中打开此文件，然后断开网络连接进行测试")
        return True
    else:
        print("❌ ECharts文件不存在")
        return False

def main():
    """主函数"""
    print("🔍 真正的离线功能测试")
    print("="*60)
    
    # 1. 测试现有HTML文件
    print("1. 测试现有HTML文件的离线兼容性...")
    html_test_passed = test_html_in_browser_simulation()
    
    if html_test_passed:
        print("✅ HTML文件离线兼容性测试通过")
    else:
        print("❌ HTML文件离线兼容性测试失败")
    
    # 2. 创建专门的离线测试环境
    print("\n2. 创建专门的离线测试环境...")
    offline_test_created = create_offline_test_environment()
    
    # 3. 总结
    print("\n" + "="*60)
    print("📊 测试结果总结:")
    print(f"   HTML离线兼容性: {'✅ 通过' if html_test_passed else '❌ 失败'}")
    print(f"   离线测试环境: {'✅ 已创建' if offline_test_created else '❌ 创建失败'}")
    
    if html_test_passed and offline_test_created:
        print("\n🎉 结论: 离线功能已正确实现")
        print("💡 建议: 在完全断网的环境下打开 offline_functionality_test.html 进行最终验证")
    else:
        print("\n⚠️ 结论: 离线功能可能存在问题，需要进一步修复")

if __name__ == "__main__":
    main()
