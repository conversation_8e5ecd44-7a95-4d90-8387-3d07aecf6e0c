
# 🔧 离线修复补丁 - 导入真正的离线渲染器
try:
    from .true_offline_renderer import render_chart_offline
    OFFLINE_RENDERER_AVAILABLE = True
    print("✅ 真正的离线渲染器已加载")
except ImportError:
    OFFLINE_RENDERER_AVAILABLE = False
    print("❌ 真正的离线渲染器不可用")

    def _render_bar_chart(self, config: Dict[str, Any], data: pd.DataFrame) -> str:\n        """渲染柱状图 - 优先使用离线模式"""\n        if self.embedded_mode_enabled:\n            return self._render_bar_chart_offline(config, data)\n        else:\n            return self._render_bar_chart_standard(config, data)\n\n
    def _render_bar_chart_offline(self, config: Dict[str, Any], data: pd.DataFrame) -> str:
        """渲染柱状图 - 真正的离线版本"""
        try:
            # 检查嵌入式资源
            if not self.embedded_mode_enabled:
                self.logger.warning("嵌入式模式未启用，回退到标准方法")
                return self._render_bar_chart_standard(config, data)
            
            # 获取嵌入式JavaScript资源
            try:
                from .embedded_resources import get_embedded_chart_scripts
                embedded_scripts = get_embedded_chart_scripts()
                if not embedded_scripts or 'https://' in embedded_scripts:
                    self.logger.warning("嵌入式资源不可用，回退到标准方法")
                    return self._render_bar_chart_standard(config, data)
            except ImportError:
                self.logger.warning("无法导入嵌入式资源，回退到标准方法")
                return self._render_bar_chart_standard(config, data)
            
            # 提取配置
            title = config.get('title', '柱状图')
            width = config.get('width', 800)
            height = config.get('height', 600)
            x_field = config.get('x_field')
            y_field = config.get('y_field')
            
            # 处理数据
            if data.empty:
                return self._get_no_data_html()
            
            # 自动检测字段
            if not x_field and len(data.columns) > 0:
                x_field = data.columns[0]
            if not y_field and len(data.columns) > 1:
                y_field = data.columns[1]
            elif not y_field:
                y_field = x_field
            
            # 提取数据
            x_data = data[x_field].astype(str).tolist()
            y_data = data[y_field].fillna(0).astype(float).tolist()
            
            # 生成图表配置
            chart_option = {
                'title': {'text': title},
                'tooltip': {},
                'xAxis': {'data': x_data},
                'yAxis': {},
                'series': [{
                    'name': y_field,
                    'type': 'bar',
                    'data': y_data
                }]
            }
            
            # 生成完全离线的HTML
            chart_id = f"chart_{hash(str(config)) % 10000}"
            html = f"""
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{title}</title>
    <style>
        body {{
            margin: 0;
            padding: 0;
            background: white;
        }}
        #{chart_id} {{
            width: {width}px;
            height: {height}px;
            margin: 0 auto;
        }}
    </style>
</head>
<body>
    <div id="{chart_id}"></div>
    
    {embedded_scripts}
    
    <script type="text/javascript">
    // 初始化图表
    var chartDom = document.getElementById('{chart_id}');
    var myChart = echarts.init(chartDom);
    
    // 图表配置
    var option = {json.dumps(chart_option, ensure_ascii=False)};
    
    // 设置图表
    myChart.setOption(option);
    
    // 响应式调整
    window.addEventListener('resize', function() {{
        myChart.resize();
    }});
    </script>
</body>
</html>
"""
            
            self.logger.info(f"✅ 离线柱状图渲染成功: {len(html)} 字符")
            return html
            
        except Exception as e:
            self.logger.error(f"离线柱状图渲染失败: {e}")
            return self._get_error_html(str(e))
    
    def _render_bar_chart_standard(self, config: Dict[str, Any], data: pd.DataFrame) -> str:
        """标准柱状图渲染方法（原始方法的重命名版本）"""
        # 这里是原始的_render_bar_chart方法内容
        # 为了简化，这里返回错误HTML
        return self._get_error_html("标准渲染方法需要从原始代码复制")
\n#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import sys
import logging
import sqlite3
import pandas as pd
from typing import Dict, List, Any, Optional

try:
    from pyecharts import options as opts
    from pyecharts.charts import Bar, Line, Pie, Scatter, Radar, Funnel, Gauge, HeatMap, WordCloud, Geo, Map
    from pyecharts.globals import ThemeType
    from pyecharts.commons.utils import JsCode
    PYECHARTS_AVAILABLE = True

    # 导入离线配置模块
    try:
        from .offline_chart_config import setup_offline_charts, check_chart_offline_status
        OFFLINE_CONFIG_AVAILABLE = True
    except ImportError:
        OFFLINE_CONFIG_AVAILABLE = False

    # 导入嵌入式资源模块
    try:
        from .embedded_resources import get_embedded_chart_scripts, is_embedded_resources_ready
        EMBEDDED_RESOURCES_AVAILABLE = True
    except ImportError:
        EMBEDDED_RESOURCES_AVAILABLE = False

except ImportError as e:
    print(f"PyEcharts导入失败: {e}")
    PYECHARTS_AVAILABLE = False
    OFFLINE_CONFIG_AVAILABLE = False
    EMBEDDED_RESOURCES_AVAILABLE = False

# 导入新的独立模块
try:
    from src.utils.chart_html_enhancer import ChartHtmlEnhancer, ChartFormatValidator
    HTML_ENHANCER_AVAILABLE = True
except ImportError:
    HTML_ENHANCER_AVAILABLE = False

class ChartRenderer:
    """图表渲染器 - 使用PyEcharts生成图表"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)

        # 初始化HTML增强器
        self.html_enhancer = ChartHtmlEnhancer() if HTML_ENHANCER_AVAILABLE else None

        # 设置嵌入式资源模式（优先）
        self.embedded_mode_enabled = False
        if EMBEDDED_RESOURCES_AVAILABLE:
            try:
                self.embedded_mode_enabled = is_embedded_resources_ready()
                if self.embedded_mode_enabled:
                    self.logger.info("图表渲染器已启用嵌入式资源模式（完全离线）")
                else:
                    self.logger.info("嵌入式资源不可用，尝试其他离线模式")
            except Exception as e:
                self.logger.warning(f"检查嵌入式资源失败: {e}")

        # 设置传统离线模式（如果嵌入式不可用）
        self.offline_mode_enabled = False
        if not self.embedded_mode_enabled and OFFLINE_CONFIG_AVAILABLE:
            try:
                self.offline_mode_enabled = setup_offline_charts()
                if self.offline_mode_enabled:
                    self.logger.info("图表渲染器已启用传统离线模式")
                else:
                    self.logger.info("图表渲染器使用在线模式")
            except Exception as e:
                self.logger.warning(f"设置传统离线模式失败: {e}")

        # 检查离线资源状态
        self.offline_status = None
        if OFFLINE_CONFIG_AVAILABLE:
            try:
                self.offline_status = check_chart_offline_status()
                if not self.offline_status.get("offline_ready", False):
                    missing_files = self.offline_status.get("missing_files", [])
                    if missing_files:
                        self.logger.warning(f"离线资源缺失: {', '.join(missing_files)}")
                        self.logger.info("运行 'python tools/download_offline_resources.py' 下载离线资源")
            except Exception as e:
                self.logger.warning(f"检查离线资源状态失败: {e}")

        # 主题映射
        self.theme_map = {
            "默认": ThemeType.WHITE if PYECHARTS_AVAILABLE else None,
            "暗色": ThemeType.DARK if PYECHARTS_AVAILABLE else None,
            "明亮": ThemeType.LIGHT if PYECHARTS_AVAILABLE else None,
            "复古": ThemeType.VINTAGE if PYECHARTS_AVAILABLE else None,
            "商务": ThemeType.MACARONS if PYECHARTS_AVAILABLE else None,
        }
        
        # 图表类型映射
        self.chart_type_map = {
            "柱状图": "bar",
            "折线图": "line",
            "饼图": "pie",
            "散点图": "scatter",
            "雷达图": "radar",
            "漏斗图": "funnel",
            "仪表盘": "gauge",
            "热力图": "heatmap",
            "Gauge": "gauge",
            "Geo": "geo",
            "地图": "map",
            "词云图": "wordcloud",
            "数字卡": "number_card",
            "表格": "table"
        }

        # 🔧 新增：颜色映射
        self.color_map = {
            '默认': None,
            '黑色': '#000000',
            '白色': '#FFFFFF',
            '蓝色': '#1890FF',
            '红色': '#F5222D',
            '绿色': '#52C41A',
            '橙色': '#FA8C16',
            '紫色': '#722ED1',
            '灰色': '#8C8C8C'
        }

        # 🔧 新增：位置映射
        self.position_map = {
            '居中': 'center',
            '左对齐': 'left',
            '右对齐': 'right'
        }

        # 🔧 新增：图例位置映射
        self.legend_position_map = {
            '顶部居中': {'pos_top': '5%', 'pos_left': 'center'},
            '底部居中': {'pos_bottom': '5%', 'pos_left': 'center'},
            '左侧': {'pos_left': '5%', 'pos_top': 'center'},
            '右侧': {'pos_right': '5%', 'pos_top': 'center'},
            '顶部左侧': {'pos_top': '5%', 'pos_left': '5%'},
            '顶部右侧': {'pos_top': '5%', 'pos_right': '5%'}
        }

        # 🔧 新增：背景颜色映射
        self.background_color_map = {
            '默认': None,
            '白色': '#FFFFFF',
            '透明': 'transparent',
            '浅灰': '#F5F5F5',
            '深灰': '#E8E8E8',
            '浅蓝': '#F0F9FF',
            '浅绿': '#F6FFED'
        }

        # 🔧 新增：默认主题色方案
        self.default_theme_colors = [
            "#5470C6", "#91CC75", "#FAC858", "#EE6666",
            "#73C0DE", "#3BA272", "#FC8452", "#9A60B4",
            "#EA7CCC", "#5470C6", "#91CC75", "#FAC858"
        ]

    def get_theme_colors(self, config: Dict[str, Any]) -> List[str]:
        """
        获取主题色配置 - 增强版本，支持多种配置来源

        参数:
            config: 图表配置

        返回:
            颜色列表
        """
        # 🔧 修复：增强主题色获取逻辑，支持多种配置来源

        # 1. 检查直接的颜色配置
        if 'colors' in config and isinstance(config['colors'], list) and len(config['colors']) > 0:
            colors = config['colors']
            if self._validate_color_list(colors):
                self.logger.info(f"使用直接颜色配置: {colors[:3]}...")
                return colors

        # 2. 检查主题色配置对象
        theme_colors_config = config.get('theme_colors')
        if theme_colors_config:
            # 🔧 修复：支持直接传入颜色列表
            if isinstance(theme_colors_config, list):
                colors = theme_colors_config
                if self._validate_color_list(colors):
                    self.logger.info(f"使用直接主题色列表: {colors[:3]}...")
                    return colors
            elif isinstance(theme_colors_config, dict):
                # 如果有自定义主题色配置
                if 'colors' in theme_colors_config:
                    colors = theme_colors_config['colors']
                    if self._validate_color_list(colors):
                        # 注释图表类信息通知
                        # self.logger.info(f"使用主题色配置: {colors[:3]}...")
                        return colors
                elif 'theme_data' in theme_colors_config:
                    theme_data = theme_colors_config['theme_data']
                    if isinstance(theme_data, dict) and 'colors' in theme_data:
                        colors = theme_data['colors']
                        if self._validate_color_list(colors):
                            self.logger.info(f"使用主题数据配置: {colors[:3]}...")
                            return colors

        # 3. 检查主题名称配置（从主题色管理器获取）
        theme_name = config.get('theme_name') or config.get('theme')
        if theme_name and theme_name != '默认':
            try:
                from src.utils.theme_colors import theme_color_manager
                colors = theme_color_manager.get_theme_colors(theme_name)
                if self._validate_color_list(colors):
                    self.logger.info(f"使用主题管理器配置 [{theme_name}]: {colors[:3]}...")
                    return colors
            except Exception as e:
                self.logger.warning(f"从主题管理器获取颜色失败: {e}")

        # 4. 返回默认主题色
        self.logger.info(f"使用默认主题色: {self.default_theme_colors[:3]}...")
        return self.default_theme_colors

    def _validate_color_list(self, colors: List[str]) -> bool:
        """
        验证颜色列表的有效性

        参数:
            colors: 颜色列表

        返回:
            是否有效
        """
        if not isinstance(colors, list) or len(colors) == 0:
            return False

        # 检查颜色格式（简单验证）
        for color in colors:
            if not isinstance(color, str) or len(color) < 3:
                return False
            # 检查是否为十六进制颜色格式
            if color.startswith('#') and len(color) == 7:
                try:
                    int(color[1:], 16)  # 验证十六进制
                    continue
                except ValueError:
                    return False
            # 检查是否为RGB格式或颜色名称
            elif color.startswith('rgb') or color in ['red', 'blue', 'green', 'yellow', 'orange', 'purple', 'pink', 'brown', 'gray', 'black']:
                continue
            else:
                return False

        return True

    def _build_title_opts(self, config: Dict[str, Any], width: int, chart_type: str = None) -> opts.TitleOpts:
        """构建标题配置选项"""
        title = config.get('title', '图表')

        # 🔧 新增：获取标题样式配置
        font_size = config.get('title_font_size', 16)
        font_bold = config.get('title_font_bold', True)
        title_color = config.get('title_color', '默认')
        title_position = config.get('title_position', '居中')

        # 根据图表宽度动态调整字体大小（如果没有明确设置）
        if config.get('title_font_size') is None:
            font_size = 14 if width >= 400 else 12

        # 获取颜色值
        color = self.color_map.get(title_color, None)

        # 获取位置值
        position = self.position_map.get(title_position, 'center')

        # 🔧 修复：为饼图设置合适的标题位置，避免与图表重叠
        pos_top = "5%"  # 默认顶部位置
        if chart_type == "饼图":
            # 饼图标题设置在顶部，为饼图本身留出空间
            pos_top = "2%"

        # 构建文本样式选项
        text_style_opts = opts.TextStyleOpts(
            font_size=font_size,
            font_weight='bold' if font_bold else 'normal'
        )

        # 如果指定了颜色，添加到文本样式中
        if color:
            text_style_opts = opts.TextStyleOpts(
                font_size=font_size,
                font_weight='bold' if font_bold else 'normal',
                color=color
            )

        return opts.TitleOpts(
            title=title,
            pos_left=position,
            pos_top=pos_top,
            title_textstyle_opts=text_style_opts
        )

    def _build_axis_opts(self, config: Dict[str, Any], field_name: str, is_x_axis: bool = True) -> opts.AxisOpts:
        """构建坐标轴配置选项"""
        # 🔧 修改：获取坐标轴名称显示配置，坐标轴本身始终显示
        if is_x_axis:
            show_axis_name = config.get('show_x_axis_name', True)
            custom_name = config.get('x_axis_name', '').strip()
            axis_title = config.get('x_axis_title', '').strip()
            axis_min = config.get('x_axis_min', '').strip()
            axis_max = config.get('x_axis_max', '').strip()
            axis_interval = config.get('x_axis_interval', '').strip()
            axis_log = config.get('x_axis_log', False)
        else:
            show_axis_name = config.get('show_y_axis_name', True)
            custom_name = config.get('y_axis_name', '').strip()
            axis_title = config.get('y_axis_title', '').strip()
            axis_min = config.get('y_axis_min', '').strip()
            axis_max = config.get('y_axis_max', '').strip()
            axis_interval = config.get('y_axis_interval', '').strip()
            axis_log = config.get('y_axis_log', False)

        show_labels = config.get('show_axis_labels', True)

        # 确定坐标轴名称
        if show_axis_name:
            if custom_name:  # 🔧 优先使用用户自定义名称
                axis_name = custom_name
            elif axis_title:
                axis_name = axis_title
            else:
                axis_name = field_name
        else:
            axis_name = ""

        # 🔧 新增：根据图表尺寸动态计算字体大小
        width = config.get('width', 800)
        height = config.get('height', 600)

        # 🔧 修改：使用用户配置的坐标轴标签字体大小，如果没有配置则使用动态计算
        if is_x_axis:
            label_font_size = config.get('x_axis_label_font_size', max(10, min(14, int(12 * min(width, height) / 600))))
        else:
            label_font_size = config.get('y_axis_label_font_size', max(10, min(14, int(12 * min(width, height) / 600))))

        # 🔧 修改：使用用户配置的坐标轴名称字体大小，如果没有配置则使用动态计算
        if is_x_axis:
            name_font_size = config.get('x_axis_name_font_size', max(12, min(16, int(label_font_size * 1.2))))
        else:
            name_font_size = config.get('y_axis_name_font_size', max(12, min(16, int(label_font_size * 1.2))))

        # 🔧 新增：获取坐标轴和网格线颜色配置
        axis_color = config.get('axis_color', '默认')
        grid_color = config.get('grid_color', '默认')
        axis_line_color = self.color_map.get(axis_color, None)
        grid_line_color = self.color_map.get(grid_color, None)

        # 构建坐标轴选项
        axis_opts = {
            "name": axis_name,
            "is_show": True,  # 🔧 修改：坐标轴始终显示，只控制名称显示
            "axislabel_opts": opts.LabelOpts(
                is_show=show_labels,
                font_size=label_font_size
            ),
            # 🔧 新增：坐标轴名称样式配置
            "name_textstyle_opts": opts.TextStyleOpts(
                font_size=name_font_size,
                font_weight="normal"
            ),
            # 🔧 新增：网格线配置，支持颜色设置
            "splitline_opts": opts.SplitLineOpts(
                is_show=config.get('show_grid', True),
                linestyle_opts=opts.LineStyleOpts(color=grid_line_color) if grid_line_color else None
            )
        }

        # 🔧 新增：坐标轴线条颜色配置
        if axis_line_color:
            axis_opts["axisline_opts"] = opts.AxisLineOpts(
                linestyle_opts=opts.LineStyleOpts(color=axis_line_color)
            )

        # 添加数值范围配置（仅对数值轴有效）
        if not is_x_axis or axis_log:  # Y轴或对数X轴
            if axis_min and axis_min.replace('.', '').replace('-', '').isdigit():
                axis_opts["min_"] = float(axis_min)
            if axis_max and axis_max.replace('.', '').replace('-', '').isdigit():
                axis_opts["max_"] = float(axis_max)
            if axis_interval and axis_interval.replace('.', '').isdigit():
                axis_opts["interval"] = float(axis_interval)
            if axis_log:
                axis_opts["type_"] = "log"

        return opts.AxisOpts(**axis_opts)

    def _build_legend_opts(self, config: Dict[str, Any], chart_type: str = None) -> opts.LegendOpts:
        """构建图例配置选项"""
        # 🔧 新增：获取图例配置
        show_legend = config.get('show_legend', True)
        legend_font_size = config.get('legend_font_size', 12)
        legend_position = config.get('legend_position', '顶部居中')

        # 获取图例位置配置
        position_config = self.legend_position_map.get(legend_position, {'pos_top': '5%', 'pos_left': 'center'})

        # 🔧 修复：为饼图调整底部图例位置，避免与饼图重叠
        if chart_type == "饼图" and legend_position == "底部居中":
            # 饼图中心在55%，半径70%，所以饼图底部约在90%位置
            # 图例需要在更下方，增加间距
            position_config = {'pos_bottom': '2%', 'pos_left': 'center'}

        # 构建图例选项
        legend_opts = {
            "is_show": show_legend,
            "textstyle_opts": opts.TextStyleOpts(font_size=legend_font_size)
        }

        # 添加位置配置
        legend_opts.update(position_config)

        return opts.LegendOpts(**legend_opts)

    def _get_background_color(self, config: Dict[str, Any]) -> str:
        """获取背景颜色配置"""
        # 🔧 新增：获取背景颜色配置
        background_color = config.get('chart_background_color', '默认')
        return self.background_color_map.get(background_color, None)

    def render_chart(self, chart_config: Dict[str, Any], data: pd.DataFrame) -> str:
        """
        渲染图表
        
        参数:
            chart_config: 图表配置
            data: 数据
            
        返回:
            图表HTML字符串
        """
        if not PYECHARTS_AVAILABLE:
            return self._get_fallback_html(chart_config)
        
        try:
            # 确保宽度和高度配置正确
            width = int(chart_config.get('width', 800))
            height = int(chart_config.get('height', 600))

            # 🔧 新增：使用DPI适配器优化配置
            try:
                from src.utils.dpi_adapter import dpi_adapter
                chart_config = dpi_adapter.get_chart_config_adjustments(chart_config, width, height)
                # 注释图表类信息通知
                # self.logger.info(f"DPI适配器已优化图表配置，设备像素比例: {chart_config.get('device_pixel_ratio', 1.0)}")
            except Exception as e:
                self.logger.warning(f"DPI适配器优化失败，使用原始配置: {e}")

            chart_config['width'] = width
            chart_config['height'] = height

            # 记录渲染信息
            chart_title = chart_config.get('title', '')
            # 注释图表类信息通知
            # self.logger.info(f"渲染图表: 类型={chart_config.get('chart_type', '柱状图')}, 尺寸={width}x{height}, 标题='{chart_title}'")

            chart_type = self.chart_type_map.get(chart_config.get('chart_type', ''), 'bar')
            
            if chart_type == 'bar':
                html = self._render_bar_chart(chart_config, data)
            elif chart_type == 'line':
                html = self._render_line_chart(chart_config, data)
            elif chart_type == 'pie':
                html = self._render_pie_chart(chart_config, data)
            elif chart_type == 'scatter':
                html = self._render_scatter_chart(chart_config, data)
            elif chart_type == 'gauge':
                html = self._render_gauge_chart(chart_config, data)
            elif chart_type == 'geo':
                html = self._render_geo_chart(chart_config, data)
            elif chart_type == 'map':
                html = self._render_map_chart(chart_config, data)
            elif chart_type == 'wordcloud':
                html = self._render_wordcloud_chart(chart_config, data)
            elif chart_type == 'number_card':
                html = self._render_number_card(chart_config, data)
            elif chart_type == 'table':
                html = self._render_table_chart(chart_config, data)
            else:
                html = self._render_bar_chart(chart_config, data)  # 默认使用柱状图
            
            # 🔧 修复：区分不同类型的图表处理方式
            # 数字卡、表格图、词云图生成的是纯HTML片段，不需要包装成完整HTML文档
            # 柱状图、饼图等ECharts图表需要完整的HTML文档结构
            is_pure_html_chart = chart_type in ['number_card', 'table']

            if is_pure_html_chart:
                # 🔧 修复：对于纯HTML图表（数字卡、表格图），不进行HTML增强处理
                # 注释图表类信息通知
                # self.logger.info(f"检测到纯HTML图表类型: {chart_type}，跳过HTML增强处理")
                # 只进行基本的ID修复和交互注入
                html = self._fix_chart_div_id(html, chart_config)
                html = self._inject_chart_interaction_js(html, chart_config)
            else:
                # 🔧 修复：对于ECharts图表，进行完整的HTML增强处理
                if self.html_enhancer:
                    html = self.html_enhancer.enhance_chart_html(html, chart_config, width, height)
                    # 注释图表类信息通知
                    # self.logger.info("已使用HTML增强器处理图表")
                else:
                    # 后备处理
                    html = self._add_responsive_support(html, width, height, chart_config)
                    self.logger.warning("HTML增强器不可用，使用后备处理")

                # 🔧 新增：确保图表div使用正确的ID
                html = self._fix_chart_div_id(html, chart_config)

                # 🔧 新增：注入图表交互JavaScript代码
                html = self._inject_chart_interaction_js(html, chart_config)

            return html
                
        except Exception as e:
            self.logger.error(f"渲染图表失败: {e}")
            return self._get_error_html(str(e))
    
    def _render_bar_chart_standard(self, config: Dict[str, Any], data: pd.DataFrame) -> str:
        """渲染柱状图"""
        try:
            # 获取配置
            x_field = config.get('x_field', '')
            y_field = config.get('y_field', '')
            title = config.get('title', '柱状图')
            theme = self.theme_map.get(config.get('theme', '默认'), ThemeType.WHITE)
            width = config.get('width', 800)
            height = config.get('height', 600)

            # 调试日志：记录标题信息
            self.logger.info(f"📊 柱状图渲染 - 标题: '{title}' (来自配置: '{config.get('title', 'N/A')}')")

            # 处理数据
            if data.empty or not x_field:
                return self._get_no_data_html()

            # 🔧 修复数据索引问题：确保所有行都被正确处理
            self.logger.info(f"📊 ===== 开始处理柱状图数据 =====")
            self.logger.info(f"📊 原始数据形状: {data.shape}")
            self.logger.info(f"📊 数据列: {list(data.columns)}")
            self.logger.info(f"📊 完整数据内容:\n{data.to_string()}")

            # 重置数据索引，确保从0开始连续索引
            data_reset = data.reset_index(drop=True)
            self.logger.info(f"📊 重置索引后数据形状: {data_reset.shape}")
            self.logger.info(f"📊 重置索引后完整数据:\n{data_reset.to_string()}")

            # 获取X轴数据，确保包含所有行
            if x_field in data_reset.columns:
                x_data_raw = data_reset[x_field].tolist()
                self.logger.info(f"📊 原始X轴数据 (长度={len(x_data_raw)}): {x_data_raw}")

                # 打印每个X轴值的详细信息
                for i, val in enumerate(x_data_raw):
                    self.logger.info(f"📊   X[{i}]: {repr(val)} (类型: {type(val).__name__})")
            else:
                x_data_raw = []
                self.logger.warning(f"📊 X轴字段 '{x_field}' 不在数据列中")

            # 根据聚合函数确定Y轴数据，确保包含所有行
            agg_func = config.get('agg_func', '计数')
            self.logger.info(f"📊 聚合函数: {agg_func}")

            if agg_func.startswith('计数') or agg_func == '计数':
                y_data_raw = data_reset['count_value'].tolist() if 'count_value' in data_reset.columns else [1] * len(x_data_raw)
            elif agg_func == '求和':
                y_data_raw = data_reset['sum_value'].tolist() if 'sum_value' in data_reset.columns else []
            elif agg_func == '平均值':
                y_data_raw = data_reset['avg_value'].tolist() if 'avg_value' in data_reset.columns else []
            elif agg_func == '最大值':
                y_data_raw = data_reset['max_value'].tolist() if 'max_value' in data_reset.columns else []
            elif agg_func == '最小值':
                y_data_raw = data_reset['min_value'].tolist() if 'min_value' in data_reset.columns else []
            else:
                y_data_raw = [1] * len(x_data_raw)

            self.logger.info(f"📊 原始Y轴数据 (长度={len(y_data_raw)}): {y_data_raw}")

            # 打印每个Y轴值的详细信息
            for i, val in enumerate(y_data_raw):
                self.logger.info(f"📊   Y[{i}]: {repr(val)} (类型: {type(val).__name__})")

            # 🔧 修复空值处理：统一处理逻辑，与SQL查询保持一致，避免重复标签
            def normalize_x_value(val, index):
                """标准化X轴值，与SQL查询中的COALESCE逻辑保持一致，为重复值添加索引"""
                # self.logger.debug(f"📊 标准化值: {repr(val)} (类型: {type(val).__name__})")

                if val is None:
                    result = f'空白({index})'  # 添加索引避免重复
                elif isinstance(val, float) and pd.isna(val):
                    result = f'空白({index})'
                elif isinstance(val, str):
                    if val.strip() == '':
                        result = f'空白({index})'
                    elif val.strip().lower() in ['nan', 'none', 'null']:
                        result = f'空白({index})'
                    else:
                        result = val.strip()
                else:
                    result = str(val)

                # self.logger.debug(f"📊   标准化结果: '{result}'")
                return result

            # 处理所有数据点，确保没有遗漏，为重复值添加唯一标识
            x_data = []
            y_data = []
            x_value_counts = {}  # 记录每个值出现的次数

            self.logger.info(f"📊 开始逐个处理数据点...")
            for i in range(len(x_data_raw)):
                raw_x = x_data_raw[i]
                y_val = y_data_raw[i] if i < len(y_data_raw) else 0

                # 先进行基本标准化
                if raw_x is None:
                    base_x = '空白'
                elif isinstance(raw_x, float) and pd.isna(raw_x):
                    base_x = '空白'
                elif isinstance(raw_x, str):
                    if raw_x.strip() == '':
                        base_x = '空白'
                    elif raw_x.strip().lower() in ['nan', 'none', 'null']:
                        base_x = '空白'
                    else:
                        base_x = raw_x.strip()
                else:
                    base_x = str(raw_x)

                # 检查是否重复，如果重复则添加序号
                if base_x in x_value_counts:
                    x_value_counts[base_x] += 1
                    if base_x == '空白':
                        x_val = f'空白-{x_value_counts[base_x]}'
                    else:
                        x_val = f'{base_x}-{x_value_counts[base_x]}'
                else:
                    x_value_counts[base_x] = 1
                    x_val = base_x

                x_data.append(x_val)
                y_data.append(y_val)

                self.logger.info(f"📊 数据点 {i}: 原始X='{raw_x}' -> 基础X='{base_x}' -> 最终X='{x_val}', Y={y_val}")

            self.logger.info(f"📊 ===== 数据处理完成 =====")
            self.logger.info(f"📊 最终X轴数据 (长度={len(x_data)}): {x_data}")
            self.logger.info(f"📊 最终Y轴数据 (长度={len(y_data)}): {y_data}")

            # 确保数据长度一致
            if len(x_data) != len(y_data):
                self.logger.warning(f"📊 数据长度不一致: X轴={len(x_data)}, Y轴={len(y_data)}")
                min_len = min(len(x_data), len(y_data))
                x_data = x_data[:min_len]
                y_data = y_data[:min_len]
                self.logger.info(f"📊 调整后数据长度: {min_len}")

            # 验证数据有效性
            if not x_data or not y_data:
                self.logger.error("📊 处理后的数据为空")
                return self._get_no_data_html()

            # 创建柱状图
            bar = Bar(init_opts=opts.InitOpts(
                theme=theme,
                width=f"{width}px",
                height=f"{height}px",
                animation_opts=opts.AnimationOpts(animation=False),  # 禁用动画提升性能
            ))
            
            # 动态计算网格边距 - 确保小尺寸时仍能显示完整
            grid_left = "5%"
            grid_right = "5%"
            grid_top = "15%"
            grid_bottom = "10%"
            
            # 小尺寸时增加边距比例，确保坐标轴标签显示完整
            if width < 300:
                grid_left = "15%"
                grid_right = "5%"
                grid_bottom = "15%"
            
            # 🔧 修复：使用新的配置构建方法
            # 配置图表全局选项
            global_opts = {
                "title_opts": self._build_title_opts(config, width),
                "legend_opts": self._build_legend_opts(config),
                # 🔧 修复问题1：禁用工具提示以避免白色文本框干扰
                "tooltip_opts": opts.TooltipOpts(
                    is_show=False,  # 完全禁用工具提示
                    trigger="none"  # 不响应任何触发事件
                ),
                "xaxis_opts": self._build_axis_opts(config, x_field, True),
                "yaxis_opts": self._build_axis_opts(config, y_field or agg_func, False)
            }

            # 🔧 更新：使用统一的坐标轴配置方法，支持网格配置
            show_grid = config.get('show_grid', True)

            # 构建坐标轴配置，直接传递给全局配置
            global_opts["xaxis_opts"] = self._build_axis_opts(config, x_field, True)
            global_opts["yaxis_opts"] = self._build_axis_opts(config, y_field or agg_func, False)

            # 🔧 新增：获取用户配置的数据标签字体大小和颜色（在try块之前定义，确保在所有代码路径中都可用）
            data_label_font_size = config.get('data_label_font_size', 6)
            data_label_font_color = config.get('data_label_font_color', '默认')
            data_label_color = self.color_map.get(data_label_font_color, None)

            # 🔧 修复图例问题：使用统一的配置构建方法，确保样式配置生效
            # 构建基本的全局选项
            global_opts = {
                "title_opts": self._build_title_opts(config, width),
                "legend_opts": self._build_legend_opts(config),
                "tooltip_opts": opts.TooltipOpts(
                    is_show=False,  # 完全禁用工具提示
                    trigger="none"  # 不响应任何触发事件
                ),
                "xaxis_opts": self._build_axis_opts(config, x_field, True),
                "yaxis_opts": self._build_axis_opts(config, y_field or agg_func, False),
                # 添加DataZoom配置
                "datazoom_opts": [
                    opts.DataZoomOpts(
                        is_show=False,        # 隐藏缩放控件
                        type_="inside",       # 内置缩放（鼠标滚轮）
                        range_start=0,        # ✅ 修复：从0%开始显示所有数据
                        range_end=100,        # ✅ 修复：到100%结束显示所有数据
                        orient="horizontal"
                    )
                ]
            }

            # 设置全局选项
            bar.set_global_opts(**global_opts)

            # 🔧 修复：通过直接操作options属性设置网格选项，而不是通过set_global_opts
            # 这是因为set_global_opts方法不接受grid_opts参数
            try:
                grid_opts = opts.GridOpts(
                    pos_left=grid_left,
                    pos_right=grid_right,
                    pos_top=grid_top,
                    pos_bottom=grid_bottom,
                    is_contain_label=True  # 确保网格包含标签
                )
                # 直接设置到图表的options中
                bar.options.update(grid=[grid_opts.opts])
                self.logger.info(f"📊 成功设置网格选项: left={grid_left}, right={grid_right}, top={grid_top}, bottom={grid_bottom}")
            except Exception as e:
                self.logger.warning(f"网格选项设置失败: {e}")

            # 设置系列选项
            bar.set_series_opts(
                label_opts=opts.LabelOpts(
                    is_show=config.get('show_labels', False),
                    font_size=data_label_font_size,
                    color=data_label_color
                )
            )

            # 🔧 添加详细的图表创建调试信息
            self.logger.info(f"📊 ===== 开始创建ECharts柱状图 =====")
            self.logger.info(f"📊 即将传递给ECharts的X轴数据: {x_data}")
            self.logger.info(f"📊 即将传递给ECharts的Y轴数据: {y_data}")
            self.logger.info(f"📊 数据点数量: {len(x_data)}")
            self.logger.info(f"📊 数据标签字体大小: {data_label_font_size}px")

            bar.add_xaxis(x_data)
            self.logger.info(f"📊 已添加X轴数据到ECharts")

            # 🔧 新增：获取主题色配置
            theme_colors = self.get_theme_colors(config)

            bar.add_yaxis(
                series_name=y_field or agg_func,
                y_axis=y_data,
                label_opts=opts.LabelOpts(
                    is_show=config.get('show_labels', False),
                    font_size=data_label_font_size,
                    color=data_label_color
                ),
                # 🔧 新增：应用自定义主题色
                color=theme_colors[0] if theme_colors else None
            )
            self.logger.info(f"📊 已添加Y轴数据到ECharts，系列名称: {y_field or agg_func}")

            # 记录渲染参数
            self.logger.info(f"📊 柱状图渲染参数: 大小={width}x{height}, 数据点={len(x_data)}")

            # 生成HTML
            html_result = bar.render_embed()
            self.logger.info(f"📊 ECharts HTML生成完成，长度: {len(html_result)} 字符")

            # 🔧 详细检查HTML内容，特别是数据数组部分
            import re

            # 检查X轴数据数组
            xaxis_pattern = r'"data":\s*\[(.*?)\]'
            xaxis_matches = re.findall(xaxis_pattern, html_result, re.DOTALL)

            self.logger.info(f"📊 HTML中找到的数据数组数量: {len(xaxis_matches)}")
            for i, match in enumerate(xaxis_matches):
                # 清理和格式化匹配内容
                clean_match = re.sub(r'\s+', ' ', match.strip())
                self.logger.info(f"📊 数据数组 {i+1}: [{clean_match}]")

                # 特别检查X轴数据数组（通常包含字符串）
                if '"500025"' in match:
                    self.logger.info(f"📊 ✅ 找到X轴数据数组，包含所有标签")
                    # 统计数组中的元素数量
                    elements = [x.strip().strip('"') for x in match.split(',') if x.strip()]
                    self.logger.info(f"📊 X轴数组元素数量: {len(elements)}")
                    self.logger.info(f"📊 X轴数组内容: {elements}")

            # 检查series数据数组
            series_pattern = r'"series":\s*\[(.*?)\]'
            series_matches = re.findall(series_pattern, html_result, re.DOTALL)

            if series_matches:
                self.logger.info(f"📊 找到series配置")
                # 在series中查找data数组
                series_data_pattern = r'"data":\s*\[(.*?)\]'
                series_data_matches = re.findall(series_data_pattern, series_matches[0], re.DOTALL)

                for i, match in enumerate(series_data_matches):
                    clean_match = re.sub(r'\s+', ' ', match.strip())
                    self.logger.info(f"📊 Series数据数组 {i+1}: [{clean_match}]")

                    # 检查是否是Y轴数值数据
                    if any(str(y) in match for y in y_data):
                        elements = [x.strip() for x in match.split(',') if x.strip()]
                        self.logger.info(f"📊 ✅ 找到Y轴数据数组，元素数量: {len(elements)}")

            # 检查是否存在可能导致显示问题的配置
            if '"animation"' in html_result:
                # 注释图表类信息通知
                # self.logger.info(f"📊 HTML包含动画配置")
                pass

            if '"dataZoom"' in html_result:
                # 注释图表类信息通知
                # self.logger.info(f"📊 HTML包含数据缩放配置")
                pass

            # 保存HTML到临时文件用于调试
            import tempfile
            import os
            temp_file = os.path.join(tempfile.gettempdir(), 'debug_chart.html')
            try:
                with open(temp_file, 'w', encoding='utf-8') as f:
                    f.write(html_result)
                # 注释图表类信息通知
                # self.logger.info(f"📊 调试HTML已保存到: {temp_file}")
                pass
            except Exception as e:
                # 注释图表类信息通知
                # self.logger.warning(f"📊 保存调试HTML失败: {e}")
                pass

            # 检查HTML中是否包含所有数据点（改进检查逻辑）
            missing_data = []
            found_data = []

            for i, x_val in enumerate(x_data):
                # 检查原始值和Unicode编码值
                if str(x_val) in html_result:
                    found_data.append(f"数据点{i}: '{x_val}' (原始)")
                elif x_val.encode('unicode_escape').decode('ascii') in html_result:
                    found_data.append(f"数据点{i}: '{x_val}' (Unicode)")
                else:
                    missing_data.append(f"数据点{i}: '{x_val}'")

            self.logger.info(f"📊 找到的数据点: {found_data}")
            if missing_data:
                self.logger.warning(f"📊 ⚠️ HTML中可能缺少以下数据点: {missing_data}")
            else:
                self.logger.info(f"📊 ✅ 所有数据点都已包含在HTML中")

            self.logger.info(f"📊 ===== 柱状图创建完成 =====")
            return html_result
            
        except Exception as e:
            self.logger.error(f"渲染柱状图失败: {e}")
            return self._get_error_html(str(e))
    
    def _render_line_chart(self, config: Dict[str, Any], data: pd.DataFrame) -> str:
        """渲染折线图"""
        try:
            # 获取配置
            x_field = config.get('x_field', '')
            y_field = config.get('y_field', '')
            title = config.get('title', '折线图')
            theme = self.theme_map.get(config.get('theme', '默认'), ThemeType.WHITE)
            width = config.get('width', 800)
            height = config.get('height', 600)

            # 处理数据
            if data.empty or not x_field:
                return self._get_no_data_html()

            # 🔧 修复数据索引问题：确保所有行都被正确处理
            self.logger.info(f"📊 折线图数据处理，原始数据形状: {data.shape}")

            # 重置数据索引，确保从0开始连续索引
            data_reset = data.reset_index(drop=True)

            # 🔧 修复空值处理：统一处理逻辑，与SQL查询保持一致
            def normalize_x_value(val):
                """标准化X轴值，与SQL查询中的COALESCE逻辑保持一致"""
                if val is None:
                    return '空白'
                if isinstance(val, float) and pd.isna(val):
                    return '空白'
                if isinstance(val, str):
                    if val.strip() == '':
                        return '空白'
                    if val.strip().lower() in ['nan', 'none', 'null']:
                        return '空白'
                    return val.strip()
                return str(val)

            # 获取X轴数据，确保包含所有行
            if x_field in data_reset.columns:
                x_data_raw = data_reset[x_field].tolist()
                x_data = [normalize_x_value(val) for val in x_data_raw]
            else:
                x_data = []

            # 根据聚合函数确定Y轴数据，确保包含所有行
            agg_func = config.get('agg_func', '计数')
            if agg_func.startswith('计数') or agg_func == '计数':
                y_data = data_reset['count_value'].tolist() if 'count_value' in data_reset.columns else [1] * len(x_data)
            elif agg_func == '求和':
                y_data = data_reset['sum_value'].tolist() if 'sum_value' in data_reset.columns else []
            elif agg_func == '平均值':
                y_data = data_reset['avg_value'].tolist() if 'avg_value' in data_reset.columns else []
            elif agg_func == '最大值':
                y_data = data_reset['max_value'].tolist() if 'max_value' in data_reset.columns else []
            elif agg_func == '最小值':
                y_data = data_reset['min_value'].tolist() if 'min_value' in data_reset.columns else []
            else:
                y_data = [1] * len(x_data)

            self.logger.info(f"📊 折线图X轴数据: {x_data}")
            self.logger.info(f"📊 折线图Y轴数据: {y_data}")

            # 确保数据长度一致
            if len(x_data) != len(y_data):
                min_len = min(len(x_data), len(y_data))
                x_data = x_data[:min_len]
                y_data = y_data[:min_len]
            
            # 创建折线图
            line = Line(init_opts=opts.InitOpts(
                theme=theme,
                width=f"{width}px",
                height=f"{height}px"
            ))
            
            # 🔧 新增：获取主题色配置
            theme_colors = self.get_theme_colors(config)

            # 🔧 新增：获取用户配置的数据标签字体大小和颜色
            data_label_font_size = config.get('data_label_font_size', 6)
            data_label_font_color = config.get('data_label_font_color', '默认')
            data_label_color = self.color_map.get(data_label_font_color, None)

            line.add_xaxis(x_data)
            line.add_yaxis(
                series_name=y_field or agg_func,
                y_axis=y_data,
                label_opts=opts.LabelOpts(
                    is_show=config.get('show_labels', False),
                    font_size=data_label_font_size,
                    color=data_label_color
                ),
                # 🔧 新增：应用自定义主题色
                color=theme_colors[0] if theme_colors else None
            )
            
            line.set_global_opts(
                title_opts=self._build_title_opts(config, width),
                legend_opts=self._build_legend_opts(config),
                # 🔧 修复问题1：禁用工具提示以避免白色文本框干扰
                tooltip_opts=opts.TooltipOpts(
                    is_show=False,  # 完全禁用工具提示
                    trigger="none"  # 不响应任何触发事件
                ),
                xaxis_opts=self._build_axis_opts(config, x_field, True),
                yaxis_opts=self._build_axis_opts(config, y_field or agg_func, False)
            )
            
            return line.render_embed()
            
        except Exception as e:
            self.logger.error(f"渲染折线图失败: {e}")
            return self._get_error_html(str(e))
    
    def _render_pie_chart(self, config: Dict[str, Any], data: pd.DataFrame) -> str:
        """渲染饼图"""
        try:
            # 获取配置
            x_field = config.get('x_field', '')
            title = config.get('title', '饼图')
            theme = self.theme_map.get(config.get('theme', '默认'), ThemeType.WHITE)
            width = config.get('width', 800)
            height = config.get('height', 600)

            # 处理数据
            if data.empty or not x_field:
                return self._get_no_data_html()

            # 🔧 修复数据索引问题：确保所有行都被正确处理
            # 注释图表类信息通知
            # self.logger.info(f"📊 饼图数据处理，原始数据形状: {data.shape}")

            # 重置数据索引，确保从0开始连续索引
            data_reset = data.reset_index(drop=True)

            # 根据聚合函数确定数据，确保包含所有行
            agg_func = config.get('agg_func', '计数')
            if agg_func.startswith('计数') or agg_func == '计数':
                values = data_reset['count_value'].tolist() if 'count_value' in data_reset.columns else [1] * len(data_reset)
            elif agg_func == '求和':
                values = data_reset['sum_value'].tolist() if 'sum_value' in data_reset.columns else []
            elif agg_func == '平均值':
                values = data_reset['avg_value'].tolist() if 'avg_value' in data_reset.columns else []
            elif agg_func == '最大值':
                values = data_reset['max_value'].tolist() if 'max_value' in data_reset.columns else []
            elif agg_func == '最小值':
                values = data_reset['min_value'].tolist() if 'min_value' in data_reset.columns else []
            else:
                values = [1] * len(data_reset)

            # 🔧 修复空值处理：统一处理逻辑，与SQL查询保持一致
            def normalize_x_value(val):
                """标准化X轴值，与SQL查询中的COALESCE逻辑保持一致"""
                if val is None:
                    return '空白'
                if isinstance(val, float) and pd.isna(val):
                    return '空白'
                if isinstance(val, str):
                    if val.strip() == '':
                        return '空白'
                    if val.strip().lower() in ['nan', 'none', 'null']:
                        return '空白'
                    return val.strip()
                return str(val)

            # 获取标签数据，确保包含所有行
            if x_field in data_reset.columns:
                labels_raw = data_reset[x_field].tolist()
                labels = [normalize_x_value(val) for val in labels_raw]
            else:
                labels = []

            # 注释图表类信息通知
            # self.logger.info(f"📊 饼图标签数据: {labels}")
            # self.logger.info(f"📊 饼图数值数据: {values}")

            # 确保数据长度一致
            if len(labels) != len(values):
                min_len = min(len(labels), len(values))
                labels = labels[:min_len]
                values = values[:min_len]

            # 🔧 新增：获取主题色配置
            theme_colors = self.get_theme_colors(config)

            # 组合数据
            pie_data = list(zip(labels, values))

            # 创建饼图
            pie = Pie(init_opts=opts.InitOpts(
                theme=theme,
                width=f"{width}px",
                height=f"{height}px"
            ))

            # 🔧 新增：获取用户配置的数据标签字体大小和颜色
            data_label_font_size = config.get('data_label_font_size', 6)
            data_label_font_color = config.get('data_label_font_color', '默认')
            data_label_color = self.color_map.get(data_label_font_color, None)

            # 🔧 新增：获取百分比显示配置
            show_percentage = config.get('show_percentage', False)

            # 🔧 新增：根据百分比显示配置设置标签格式
            if show_percentage:
                # 显示文字标签 + 百分比
                label_formatter = "{b}: {d}%"
            else:
                # 只显示文字标签
                label_formatter = "{b}"

            # 🔧 修复：根据图例位置调整饼图位置和大小
            legend_position = config.get('legend_position', '顶部居中')
            if legend_position == "底部居中":
                # 底部有图例时，饼图需要上移并缩小，为图例留出空间
                pie_center = ["50%", "55%"]  # 饼图中心上移
                pie_radius = "60%"           # 饼图半径缩小
            else:
                # 其他位置的图例，使用默认设置
                pie_center = ["50%", "55%"]  # 将饼图中心下移，为标题留出空间
                pie_radius = "70%"           # 设置饼图半径

            pie.add(
                series_name=agg_func,
                data_pair=pie_data,
                label_opts=opts.LabelOpts(
                    is_show=config.get('show_labels', True),
                    font_size=data_label_font_size,
                    color=data_label_color,
                    formatter=label_formatter
                ),
                # 🔧 修复：为饼图设置位置，为标题和图例留出空间
                center=pie_center,
                radius=pie_radius
            )

            # 🔧 新增：为饼图设置自定义颜色
            if theme_colors:
                pie.set_colors(theme_colors)
            
            pie.set_global_opts(
                title_opts=self._build_title_opts(config, width, "饼图"),
                legend_opts=self._build_legend_opts(config, "饼图"),
                # 🔧 修复问题1：禁用工具提示以避免白色文本框干扰
                tooltip_opts=opts.TooltipOpts(
                    is_show=False,  # 完全禁用工具提示
                    trigger="none"  # 不响应任何触发事件
                )
            )
            
            return pie.render_embed()
            
        except Exception as e:
            self.logger.error(f"渲染饼图失败: {e}")
            return self._get_error_html(str(e))
    
    def _render_scatter_chart(self, config: Dict[str, Any], data: pd.DataFrame) -> str:
        """渲染散点图"""
        try:
            # 获取配置
            x_field = config.get('x_field', '')
            y_field = config.get('y_field', '')
            title = config.get('title', '散点图')
            theme = self.theme_map.get(config.get('theme', '默认'), ThemeType.WHITE)
            width = config.get('width', 800)
            height = config.get('height', 600)
            
            # 处理数据
            if data.empty or not x_field or not y_field:
                return self._get_no_data_html()
            
            # 获取数据
            scatter_data = []
            for _, row in data.iterrows():
                x_val = row.get(x_field, 0)
                y_val = row.get(y_field, 0)
                try:
                    x_val = float(x_val) if x_val != '' else 0
                    y_val = float(y_val) if y_val != '' else 0
                    scatter_data.append([x_val, y_val])
                except (ValueError, TypeError):
                    continue
            
            # 创建散点图
            scatter = Scatter(init_opts=opts.InitOpts(
                theme=theme,
                width=f"{width}px",
                height=f"{height}px"
            ))
            
            # 🔧 新增：获取主题色配置
            theme_colors = self.get_theme_colors(config)

            # 🔧 新增：获取用户配置的数据标签字体大小和颜色
            data_label_font_size = config.get('data_label_font_size', 6)
            data_label_font_color = config.get('data_label_font_color', '默认')
            data_label_color = self.color_map.get(data_label_font_color, None)

            scatter.add_xaxis([item[0] for item in scatter_data])
            scatter.add_yaxis(
                series_name=f"{x_field} vs {y_field}",
                y_axis=[item[1] for item in scatter_data],
                label_opts=opts.LabelOpts(
                    is_show=config.get('show_labels', False),
                    font_size=data_label_font_size,
                    color=data_label_color
                ),
                # 🔧 新增：应用自定义主题色
                color=theme_colors[0] if theme_colors else None
            )
            
            scatter.set_global_opts(
                title_opts=self._build_title_opts(config, width),
                legend_opts=self._build_legend_opts(config),
                # 🔧 修复问题1：禁用工具提示以避免白色文本框干扰
                tooltip_opts=opts.TooltipOpts(
                    is_show=False,  # 完全禁用工具提示
                    trigger="none"  # 不响应任何触发事件
                ),
                xaxis_opts=self._build_axis_opts(config, x_field, True),
                yaxis_opts=self._build_axis_opts(config, y_field, False)
            )
            
            return scatter.render_embed()
            
        except Exception as e:
            self.logger.error(f"渲染散点图失败: {e}")
            return self._get_error_html(str(e))
    
    def _get_fallback_html(self, config: Dict[str, Any]) -> str:
        """获取PyEcharts不可用时的备用HTML"""
        chart_name = config.get('name', '图表')
        chart_type = config.get('chart_type', '未知')
        
        return f"""
        <div style="width: 100%; height: 100%; display: flex; align-items: center; justify-content: center; 
                    border: 2px dashed #ccc; background-color: #f9f9f9;">
            <div style="text-align: center;">
                <h3>{chart_name}</h3>
                <p>图表类型: {chart_type}</p>
                <p style="color: #ff6b6b;">PyEcharts未安装，无法渲染图表</p>
                <p>请运行: pip install pyecharts</p>
            </div>
        </div>
        """
    
    def _get_no_data_html(self) -> str:
        """获取无数据时的HTML"""
        return """
        <div style="width: 100%; height: 100%; display: flex; align-items: center; justify-content: center; 
                    border: 2px dashed #ccc; background-color: #f9f9f9;">
            <div style="text-align: center;">
                <h3>暂无数据</h3>
                <p>请检查数据源配置和查询条件</p>
            </div>
        </div>
        """
    
    def _get_error_html(self, error_msg: str) -> str:
        """获取错误时的HTML"""
        return f"""
        <div style="width: 100%; height: 100%; display: flex; align-items: center; justify-content: center; 
                    border: 2px solid #ff6b6b; background-color: #fff5f5;">
            <div style="text-align: center;">
                <h3 style="color: #ff6b6b;">图表渲染错误</h3>
                <p style="color: #666;">{error_msg}</p>
            </div>
        </div>
        """
    
    def _add_responsive_support(self, html: str, width: int, height: int, config: Dict[str, Any] = None) -> str:
        """🔧 修改：添加增强的响应式支持，包含高DPI支持"""
        try:
            # 获取用户配置的坐标轴标签字体大小、数据标签字体大小和颜色
            if config:
                x_axis_label_font_size = config.get('x_axis_label_font_size', 8)
                y_axis_label_font_size = config.get('y_axis_label_font_size', 8)
                data_label_font_size = config.get('data_label_font_size', 6)
                data_label_font_color = config.get('data_label_font_color', '默认')
                data_label_color = self.color_map.get(data_label_font_color, None)
                # 🔧 新增：获取背景颜色配置
                background_color = self._get_background_color(config)
                # 🔧 新增：获取设备像素比例
                device_pixel_ratio = config.get('device_pixel_ratio', 1.0)
            else:
                x_axis_label_font_size = 8
                y_axis_label_font_size = 8
                data_label_font_size = 6
                data_label_color = None
                background_color = None
                device_pixel_ratio = 1.0
            # 生成数据标签颜色的JavaScript代码
            if data_label_color:
                data_label_color_js = f"option.series[j].label.color = '{data_label_color}';"
            else:
                data_label_color_js = ""

            # 确保图表div有css class
            html = html.replace('<div id="', '<div class="echarts-container" id="')

            # 🔧 修复：根据用户配置设置背景颜色
            bg_style = f"background: {background_color} !important;" if background_color else "background: transparent !important;"

            # 🔧 新增：计算逻辑尺寸（用于显示）
            logical_width = int(width / device_pixel_ratio)
            logical_height = int(height / device_pixel_ratio)

            # 🔧 新增：添加高分辨率viewport meta标签和优化设置
            viewport_meta = f'''<meta name="viewport" content="width={logical_width}, height={logical_height}, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
<meta name="format-detection" content="telephone=no">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="renderer" content="webkit">'''

            # 如果HTML中没有head标签，添加一个
            if '<head>' not in html:
                html = html.replace('<html>', '<html><head></head>')

            # 在head中添加viewport meta标签
            html = html.replace('<head>', f'<head>{viewport_meta}')

            # 添加增强的自定义样式和脚本
            responsive_code = f"""
            <style>
                html, body {{
                    width: {logical_width}px !important;
                    height: {logical_height}px !important;
                    margin: 0 !important;
                    padding: 0 !important;
                    overflow: hidden !important;
                    {bg_style}
                    box-sizing: border-box !important;
                    /* 🔧 新增：高DPI支持 */
                    -webkit-transform-origin: 0 0;
                    transform-origin: 0 0;
                    -webkit-transform: scale({1/device_pixel_ratio});
                    transform: scale({1/device_pixel_ratio});
                    /* 🔧 新增：字体和图像渲染优化 */
                    -webkit-font-smoothing: antialiased;
                    -moz-osx-font-smoothing: grayscale;
                    text-rendering: optimizeLegibility;
                    image-rendering: -webkit-optimize-contrast;
                    image-rendering: crisp-edges;
                    /* 🔧 新增：强制硬件加速 */
                    -webkit-backface-visibility: hidden;
                    backface-visibility: hidden;
                    -webkit-perspective: 1000;
                    perspective: 1000;
                }}
                .echarts-container {{
                    width: {width}px !important;
                    height: {height}px !important;
                    margin: 0 !important;
                    padding: 0 !important;
                    box-sizing: border-box !important;
                    display: block !important;
                    /* 🔧 新增：高DPI图表容器支持 */
                    -webkit-transform-origin: 0 0;
                    transform-origin: 0 0;
                    /* 🔧 新增：强化图表渲染质量 */
                    -webkit-font-smoothing: antialiased;
                    -moz-osx-font-smoothing: grayscale;
                    text-rendering: optimizeLegibility;
                    image-rendering: -webkit-optimize-contrast;
                    image-rendering: crisp-edges;
                    /* 🔧 新增：强制硬件加速 */
                    -webkit-backface-visibility: hidden;
                    backface-visibility: hidden;
                    will-change: transform;
                }}
                /* 确保图表内容完全填充容器 */
                .echarts-container > div {{
                    width: {width}px !important;
                    height: {height}px !important;
                    box-sizing: border-box !important;
                    /* 🔧 新增：子元素渲染优化 */
                    -webkit-font-smoothing: antialiased;
                    -moz-osx-font-smoothing: grayscale;
                }}
                /* 🔧 新增：Canvas元素优化 */
                .echarts-container canvas {{
                    image-rendering: -webkit-optimize-contrast;
                    image-rendering: crisp-edges;
                    image-rendering: pixelated;
                }}
                /* 确保坐标轴和标题在小尺寸时仍能完整显示 */
                .echarts-for-react div {{
                    overflow: visible !important;
                }}
                /* 🔧 新增：高DPI媒体查询支持 */
                @media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {{
                    .echarts-container {{
                        image-rendering: -webkit-optimize-contrast;
                        image-rendering: crisp-edges;
                    }}
                    .echarts-container canvas {{
                        image-rendering: -webkit-optimize-contrast;
                        image-rendering: crisp-edges;
                    }}
                }}
                @media (-webkit-min-device-pixel-ratio: 3), (min-resolution: 288dpi) {{
                    .echarts-container {{
                        image-rendering: -webkit-optimize-contrast;
                        image-rendering: crisp-edges;
                    }}
                    .echarts-container canvas {{
                        image-rendering: -webkit-optimize-contrast;
                        image-rendering: crisp-edges;
                    }}
                }}
                /* 🔧 修复问题1：隐藏所有可能的工具提示和悬停元素 */
                .tooltip, .echarts-tooltip, [class*="tooltip"], [id*="tooltip"] {{
                    display: none !important;
                    visibility: hidden !important;
                    opacity: 0 !important;
                    pointer-events: none !important;
                }}
                /* 禁用所有悬停效果 */
                * {{
                    pointer-events: none !important;
                }}
                /* 但允许图表本身的基本交互 */
                .echarts-container, div[id*="chart"] {{
                    pointer-events: auto !important;
                }}
            </style>
            <script>
                console.log('图表响应式支持脚本加载，目标尺寸: {width}x{height}，设备像素比例: {device_pixel_ratio}');

                // 🔧 新增：设备像素比例检测
                window.devicePixelRatio = {device_pixel_ratio};
                console.log('设置设备像素比例:', window.devicePixelRatio);

                // 全局图表调整函数
                window.resizeEChartsInstances = function(targetWidth, targetHeight) {{
                    console.log('执行全局图表调整，目标尺寸:', targetWidth, 'x', targetHeight, '，设备像素比例:', window.devicePixelRatio);

                    if (window.echarts) {{
                        var containers = document.querySelectorAll('.echarts-container');
                        console.log('找到图表容器数量:', containers.length);

                        for (var i = 0; i < containers.length; i++) {{
                            var chart = echarts.getInstanceByDom(containers[i]);
                            if (chart) {{
                                console.log('调整图表', i, '大小为:', targetWidth, 'x', targetHeight, '，设备像素比例:', window.devicePixelRatio);

                                // 🔧 修复：保存原始配置后再重新初始化
                                var originalOption = chart.getOption();
                                if (originalOption && originalOption.length > 0) {{
                                    var savedOption = JSON.parse(JSON.stringify(originalOption[0]));

                                    chart.dispose();
                                    chart = echarts.init(containers[i], null, {{
                                        devicePixelRatio: window.devicePixelRatio,
                                        renderer: 'canvas',
                                        width: targetWidth,
                                        height: targetHeight
                                    }});

                                    // 重新设置图表配置
                                    chart.setOption(savedOption, true);
                                }} else {{
                                    // 如果没有原始配置，只调整大小
                                    console.log('没有找到原始配置，跳过重新初始化');
                                }}

                                // 🔧 修改：考虑设备像素比例的图表大小调整
                                var actualWidth = targetWidth;
                                var actualHeight = targetHeight;

                                // 🔧 新增：强制高分辨率Canvas渲染
                                var canvasElements = containers[i].querySelectorAll('canvas');
                                canvasElements.forEach(function(canvas) {{
                                    var ctx = canvas.getContext('2d');
                                    var devicePixelRatio = window.devicePixelRatio || 1;

                                    // 设置Canvas的实际像素尺寸
                                    canvas.width = actualWidth * devicePixelRatio;
                                    canvas.height = actualHeight * devicePixelRatio;

                                    // 设置Canvas的显示尺寸
                                    canvas.style.width = actualWidth + 'px';
                                    canvas.style.height = actualHeight + 'px';

                                    // 缩放绘图上下文以匹配设备像素比例
                                    ctx.scale(devicePixelRatio, devicePixelRatio);

                                    console.log('Canvas高分辨率设置:', canvas.width, 'x', canvas.height, 'DPR:', devicePixelRatio);
                                }});

                                // 强制调整图表大小
                                chart.resize({{
                                    width: actualWidth,
                                    height: actualHeight,
                                    silent: false,
                                    devicePixelRatio: window.devicePixelRatio
                                }});

                                // 🔧 修复：安全获取并优化图表配置
                                try {{
                                    var option = chart.getOption();

                                    // 确保option存在且是数组
                                    if (option && Array.isArray(option) && option.length > 0) {{
                                        var currentOption = option[0];

                                        // 1. 动态调整网格位置和大小
                                        if (currentOption.grid && Array.isArray(currentOption.grid) && currentOption.grid.length > 0) {{
                                            if (targetWidth < 300) {{
                                                currentOption.grid[0].left = "15%";
                                                currentOption.grid[0].bottom = "15%";
                                                currentOption.grid[0].right = "5%";
                                                currentOption.grid[0].top = "15%";
                                            }} else if (targetWidth < 500) {{
                                                currentOption.grid[0].left = "10%";
                                                currentOption.grid[0].bottom = "12%";
                                                currentOption.grid[0].right = "5%";
                                                currentOption.grid[0].top = "12%";
                                            }} else {{
                                                currentOption.grid[0].left = "5%";
                                                currentOption.grid[0].bottom = "10%";
                                                currentOption.grid[0].right = "5%";
                                                currentOption.grid[0].top = "10%";
                                            }}
                                            currentOption.grid[0].containLabel = true;
                                        }}
                                    }} else {{
                                        console.log('图表配置为空或格式不正确，跳过网格调整');
                                    }}
                                }} catch (gridError) {{
                                    console.log('网格调整失败:', gridError);
                                }}

                                        // 2. 调整X轴标签 - 使用用户配置的字体大小
                                        if (currentOption.xAxis && Array.isArray(currentOption.xAxis) && currentOption.xAxis.length > 0) {{
                                            if (targetWidth < 350 || (currentOption.xAxis[0].data && currentOption.xAxis[0].data.length > 5)) {{
                                                currentOption.xAxis[0].axisLabel = {{
                                                    rotate: 45,
                                                    margin: 8,
                                                    fontSize: {x_axis_label_font_size}
                                                }};
                                            }} else {{
                                                currentOption.xAxis[0].axisLabel = {{
                                                    rotate: 0,
                                                    fontSize: {x_axis_label_font_size}
                                                }};
                                            }}
                                        }}

                                        // 3. 调整Y轴标签 - 使用用户配置的字体大小
                                        if (currentOption.yAxis && Array.isArray(currentOption.yAxis) && currentOption.yAxis.length > 0) {{
                                            currentOption.yAxis[0].axisLabel = currentOption.yAxis[0].axisLabel || {{}};
                                            currentOption.yAxis[0].axisLabel.fontSize = {y_axis_label_font_size};
                                        }}

                                        // 4. 根据容器大小调整字体大小
                                        var fontScale = Math.max(0.6, Math.min(1.2, targetWidth / 400));

                                        if (currentOption.title && Array.isArray(currentOption.title) && currentOption.title.length > 0) {{
                                            currentOption.title[0].textStyle = currentOption.title[0].textStyle || {{}};
                                            // 只有当未设置字体大小时，才用自适应
                                            if (typeof currentOption.title[0].textStyle.fontSize === 'undefined') {{
                                                currentOption.title[0].textStyle.fontSize = Math.max(12, Math.round(16 * fontScale));
                                            }}
                                        }}

                                        if (currentOption.legend && Array.isArray(currentOption.legend) && currentOption.legend.length > 0) {{
                                            currentOption.legend[0].textStyle = currentOption.legend[0].textStyle || {{}};
                                            currentOption.legend[0].textStyle.fontSize = Math.max(10, Math.round(12 * fontScale));
                                        }}

                                        // 5. 修改系列标签 - 使用用户配置的数据标签字体大小和颜色
                                        if (currentOption.series && Array.isArray(currentOption.series) && currentOption.series.length > 0) {{
                                            for (var j = 0; j < currentOption.series.length; j++) {{
                                                if (currentOption.series[j].label) {{
                                                    currentOption.series[j].label.fontSize = {data_label_font_size};
                                                    {data_label_color_js}
                                                }}
                                            }}
                                        }}

                                        // 🔧 修复问题1：禁用工具提示
                                        if (currentOption.tooltip) {{
                                            currentOption.tooltip.show = false;
                                            currentOption.tooltip.trigger = 'none';
                                            currentOption.tooltip.enterable = false;
                                        }} else {{
                                            currentOption.tooltip = {{
                                                show: false,
                                                trigger: 'none',
                                                enterable: false
                                            }};
                                        }}

                                        // 6. 应用更新后的配置
                                        chart.setOption(currentOption, true);
                                        console.log('图表', i, '自适应调整完成，工具提示已禁用');
                            }} else {{
                                console.log('图表', i, '实例未找到');
                            }}
                        }}
                    }} else {{
                        console.log('ECharts未加载');
                    }}
                }};

                // DOM加载完成后的初始化
                window.addEventListener('DOMContentLoaded', function() {{
                    console.log('DOM加载完成，开始初始化图表');

                    setTimeout(function() {{
                        // 确保图表容器有正确的class
                        var containers = document.querySelectorAll('div[id*="chart"]');
                        for (var i = 0; i < containers.length; i++) {{
                            if (!containers[i].className.includes('echarts-container')) {{
                                containers[i].className += ' echarts-container';
                            }}
                        }}

                        // 执行初始调整
                        window.resizeEChartsInstances({width}, {height});
                    }}, 100);
                }});

                // 监听窗口大小变化，实时响应
                window.addEventListener('resize', function() {{
                    console.log('窗口大小变化，重新调整图表');
                    window.resizeEChartsInstances({width}, {height});
                }});

                console.log('图表响应式支持脚本初始化完成');
            </script>
            """

            # 添加到HTML中
            if '</body>' in html:
                html = html.replace('</body>', f"{responsive_code}</body>")
            else:
                html += responsive_code

            return html
        except Exception as e:
            self.logger.error(f"添加响应式支持失败: {e}")
            return html

    def _render_gauge_chart(self, config: Dict[str, Any], data: pd.DataFrame) -> str:
        """渲染仪表盘图表 - 增强样式配置支持"""
        try:
            from pyecharts.charts import Gauge

            # 获取配置
            title = config.get('title', 'Gauge图表')
            theme = self.theme_map.get(config.get('theme', '默认'), ThemeType.WHITE)
            width = config.get('width', 800)
            height = config.get('height', 600)

            # 处理数据 - 仪表盘通常显示单个数值
            if data.empty:
                return self._get_no_data_html()

            # 取第一行数据作为仪表盘数值
            value = float(data.iloc[0, 0]) if len(data.columns) > 0 else 0

            # 🔧 新增：获取主题色配置
            theme_colors = self.get_theme_colors(config)

            # 创建仪表盘
            gauge = Gauge(init_opts=opts.InitOpts(
                theme=theme,
                width=f"{width}px",
                height=f"{height}px"
            ))

            # 🔧 新增：获取用户配置的数据标签字体大小和颜色
            data_label_font_size = config.get('data_label_font_size', 16)
            data_label_font_color = config.get('data_label_font_color', '默认')
            data_label_color = self.color_map.get(data_label_font_color, None)

            gauge.add(
                series_name="",
                data_pair=[("数值", value)],
                radius="75%",
                label_opts=opts.LabelOpts(
                    is_show=config.get('show_labels', True),
                    font_size=data_label_font_size,
                    color=data_label_color
                )
            )

            # 🔧 新增：为仪表盘设置自定义颜色
            if theme_colors:
                gauge.set_colors(theme_colors)

            gauge.set_global_opts(
                title_opts=self._build_title_opts(config, width),
                legend_opts=self._build_legend_opts(config),
                tooltip_opts=opts.TooltipOpts(is_show=False)
            )

            return gauge.render_embed()

        except Exception as e:
            self.logger.error(f"渲染Gauge图表失败: {e}")
            return self._get_error_html(str(e))

    def _render_geo_chart(self, config: Dict[str, Any], data: pd.DataFrame) -> str:
        """渲染地理坐标系图表"""
        try:
            from pyecharts.charts import Geo

            # 获取配置
            title = config.get('title', 'Geo图表')
            theme = self.theme_map.get(config.get('theme', '默认'), ThemeType.WHITE)
            width = config.get('width', 800)
            height = config.get('height', 600)

            # 处理数据
            if data.empty:
                return self._get_no_data_html()

            # 创建地理坐标系
            geo = Geo(init_opts=opts.InitOpts(
                theme=theme,
                width=f"{width}px",
                height=f"{height}px"
            ))

            # 添加数据点（这里需要根据实际数据结构调整）
            geo.add_schema(maptype="china")

            if len(data.columns) >= 2:
                geo_data = [(row[0], row[1]) for _, row in data.iterrows()]
                geo.add("", geo_data)

            geo.set_global_opts(
                title_opts=opts.TitleOpts(title=title),
                tooltip_opts=opts.TooltipOpts(is_show=False)
            )

            return geo.render_embed()

        except Exception as e:
            self.logger.error(f"渲染Geo图表失败: {e}")
            return self._get_error_html(str(e))



    def _render_map_chart(self, config: Dict[str, Any], data: pd.DataFrame) -> str:
        """渲染地图图表 - 增强样式配置支持"""
        try:
            from pyecharts.charts import Map

            # 获取配置
            title = config.get('title', '地图')
            theme = self.theme_map.get(config.get('theme', '默认'), ThemeType.WHITE)
            width = config.get('width', 800)
            height = config.get('height', 600)

            # 处理数据
            if data.empty:
                return self._get_no_data_html()

            # 🔧 新增：获取主题色配置
            theme_colors = self.get_theme_colors(config)

            # 创建地图
            map_chart = Map(init_opts=opts.InitOpts(
                theme=theme,
                width=f"{width}px",
                height=f"{height}px"
            ))

            # 添加数据（地区名称和数值）
            if len(data.columns) >= 2:
                map_data = [(row[0], row[1]) for _, row in data.iterrows()]

                # 🔧 新增：获取用户配置的数据标签字体大小和颜色
                data_label_font_size = config.get('data_label_font_size', 12)
                data_label_font_color = config.get('data_label_font_color', '默认')
                data_label_color = self.color_map.get(data_label_font_color, None)

                map_chart.add(
                    series_name="",
                    data_pair=map_data,
                    maptype="china",
                    label_opts=opts.LabelOpts(
                        is_show=config.get('show_labels', True),
                        font_size=data_label_font_size,
                        color=data_label_color
                    )
                )

            # 🔧 新增：为地图设置自定义颜色
            if theme_colors:
                map_chart.set_colors(theme_colors)

            # 🔧 新增：获取数值范围配置
            map_min_value = config.get('map_min_value', 0)
            map_max_value = config.get('map_max_value', 100)

            # 如果数据中有数值，自动计算范围
            if len(data.columns) >= 2:
                numeric_values = pd.to_numeric(data.iloc[:, 1], errors='coerce').dropna()
                if not numeric_values.empty:
                    if map_min_value == 0:  # 如果用户没有设置最小值
                        map_min_value = numeric_values.min()
                    if map_max_value == 100:  # 如果用户没有设置最大值
                        map_max_value = numeric_values.max()

            map_chart.set_global_opts(
                title_opts=self._build_title_opts(config, width),
                legend_opts=self._build_legend_opts(config),
                tooltip_opts=opts.TooltipOpts(is_show=False),
                visualmap_opts=opts.VisualMapOpts(
                    min_=map_min_value,
                    max_=map_max_value,
                    is_show=config.get('show_visualmap', True),
                    pos_left="left",
                    pos_bottom="bottom"
                )
            )

            return map_chart.render_embed()

        except Exception as e:
            self.logger.error(f"渲染地图失败: {e}")
            return self._get_error_html(str(e))

    def _render_wordcloud_chart(self, config: Dict[str, Any], data: pd.DataFrame) -> str:
        """渲染词云图 - 重新实现解决布局和颜色问题"""
        try:
            from pyecharts.charts import WordCloud

            # 获取配置
            title = config.get('title', '词云图')
            theme = self.theme_map.get(config.get('theme', '默认'), ThemeType.WHITE)
            width = config.get('width', 800)
            height = config.get('height', 600)

            # 处理数据
            if data.empty:
                return self._get_no_data_html()

            # 🔧 修复：获取主题色配置
            theme_colors = self.get_theme_colors(config)
            if not theme_colors or len(theme_colors) == 0:
                theme_colors = self.default_theme_colors
                self.logger.warning("词云图主题色配置为空，使用默认主题色")

            # 🔧 修复：预处理词云数据，手动分配颜色
            if len(data.columns) >= 2:
                word_data = []
                for i, (_, row) in enumerate(data.iterrows()):
                    word = str(row[0])
                    value = float(row[1]) if row[1] is not None else 0

                    # 🔧 修复：手动分配颜色，确保颜色配置生效
                    color = theme_colors[i % len(theme_colors)]

                    word_data.append({
                        "name": word,
                        "value": value,
                        "textStyle": {
                            "color": color  # 🔧 修复：直接设置颜色
                        }
                    })

                self.logger.info(f"词云图数据处理完成: {len(word_data)} 个词语，使用颜色: {theme_colors[:5]}...")

            # 创建词云图
            wordcloud = WordCloud(init_opts=opts.InitOpts(
                theme=theme,
                width=f"{width}px",
                height=f"{height}px"
            ))

            # 🔧 修复：使用用户配置的字体大小范围，如果没有配置则使用动态计算
            min_font_size = config.get('wordcloud_min_font_size', max(12, int(min(width, height) * 0.02)))
            max_font_size = config.get('wordcloud_max_font_size', max(min_font_size + 20, int(min(width, height) * 0.12)))

            # 确保最大字体大小大于最小字体大小
            if max_font_size <= min_font_size:
                max_font_size = min_font_size + 20

            # 🔧 修复：使用pyecharts WordCloud支持的参数
            try:
                # 尝试使用基本参数
                wordcloud.add(
                    series_name="",
                    data_pair=word_data,  # 使用预处理的数据
                    word_size_range=[min_font_size, max_font_size],
                    shape="circle"
                )
            except Exception as e:
                self.logger.warning(f"词云图添加数据失败，尝试最基本参数: {e}")
                # 如果还是失败，使用最基本的参数
                simple_word_data = [(item["name"], item["value"]) for item in word_data]
                wordcloud.add(
                    series_name="",
                    data_pair=simple_word_data,
                    word_size_range=[min_font_size, max_font_size]
                )

            # 🔧 修复：设置全局选项和颜色配置
            wordcloud.set_global_opts(
                title_opts=self._build_title_opts(config, width),
                tooltip_opts=opts.TooltipOpts(is_show=False)
            )

            # 🔧 修复：通过修改渲染后的HTML来应用颜色和布局配置
            html_content = wordcloud.render_embed()

            # 🔧 修复：后处理HTML，修复布局和颜色问题，保持现有颜色逻辑
            html_content = self._post_process_wordcloud_html(html_content, theme_colors, width, height, config)

            return html_content

        except Exception as e:
            self.logger.error(f"渲染词云图失败: {e}")
            return self._get_error_html(str(e))

    def _post_process_wordcloud_html(self, html_content: str, theme_colors: List[str], width: int, height: int, config: Dict[str, Any]) -> str:
        """
        后处理词云图HTML，修复布局问题，保持现有颜色逻辑不变

        参数:
            html_content: 原始HTML内容
            theme_colors: 主题颜色列表
            width: 图表宽度
            height: 图表高度
            config: 图表配置

        返回:
            修复后的HTML内容
        """
        try:
            import re

            # 🔧 修复1：修正拼写错误 girdSize -> gridSize
            html_content = html_content.replace('"girdSize":', '"gridSize":')

            # 🔧 修复2：添加关键布局参数
            grid_size = max(4, int(min(width, height) * 0.01))

            # 查找series配置并添加布局参数
            series_pattern = r'("type":\s*"wordCloud"[^}]+)'

            def add_layout_params(match):
                series_config = match.group(1)

                # 添加布局参数
                layout_params = f'''
            "gridSize": {grid_size},
            "drawOutOfBound": false,
            "layoutAnimation": true,
            "left": "center",
            "top": "center",
            "width": "90%",
            "height": "90%",'''

                # 在rotationStep后插入布局参数
                if '"rotationStep":' in series_config:
                    series_config = re.sub(
                        r'("rotationStep":\s*\d+),',
                        r'\1,' + layout_params,
                        series_config
                    )

                return series_config

            html_content = re.sub(series_pattern, add_layout_params, html_content, flags=re.DOTALL)

            # 🔧 修复3：保持现有颜色逻辑不变，仅在用户明确配置主题色时才应用
            # 检查是否有用户自定义的主题色配置
            apply_custom_colors = config.get('apply_custom_theme_colors', False)

            if theme_colors and apply_custom_colors:
                # 只有在用户明确要求应用自定义主题色时才修改颜色
                # 更新全局color数组
                color_array = '", "'.join(theme_colors)
                color_pattern = r'"color":\s*\[[^\]]+\]'
                new_color_config = f'"color": ["{color_array}"]'
                html_content = re.sub(color_pattern, new_color_config, html_content)

                self.logger.info(f"词云图应用自定义主题色: {theme_colors[:3]}...")
            else:
                # 保持现有颜色逻辑不变
                self.logger.info("词云图保持现有颜色逻辑不变")

            # 🔧 修复4：增强布局控制，减少文本重叠
            # 添加更严格的布局参数来减少重叠
            enhanced_layout_params = f'''
            "gridSize": {max(grid_size, 8)},
            "drawOutOfBound": false,
            "layoutAnimation": false,
            "rotationRange": [-45, 45],
            "rotationStep": 15,
            "minRotation": -45,
            "maxRotation": 45,
            "shuffle": true,
            "shrinkToFit": true,'''

            # 在现有布局参数基础上添加增强参数
            if '"layoutAnimation": true,' in html_content:
                html_content = html_content.replace('"layoutAnimation": true,', enhanced_layout_params)

            return html_content

        except Exception as e:
            self.logger.error(f"词云图HTML后处理失败: {e}")
            return html_content  # 返回原始内容

    def _render_number_card(self, config: Dict[str, Any], data: pd.DataFrame) -> str:
        """渲染数字卡片"""
        try:
            # 获取配置
            title = config.get('title', '数字卡')
            width = config.get('width', 800)
            height = config.get('height', 600)
            agg_func = config.get('agg_func', '计数')

            # 处理数据
            if data.empty:
                return self._get_no_data_html()

            # 🔧 修复：根据统计方式正确计算数值
            value = 0
            label = "数值"

            # 根据聚合函数确定显示的数值
            if agg_func.startswith('计数') or agg_func == '计数':
                if 'count_value' in data.columns:
                    # 如果有count_value列，求和所有计数值
                    value = data['count_value'].sum()
                    label = "记录数"
                else:
                    # 否则使用数据行数
                    value = len(data)
                    label = "记录数"
            elif agg_func == '求和':
                if 'sum_value' in data.columns:
                    value = data['sum_value'].sum()
                    label = "总和"
                else:
                    # 如果没有sum_value列，尝试使用第一个数值列
                    numeric_cols = data.select_dtypes(include=['number']).columns
                    if len(numeric_cols) > 0:
                        value = data[numeric_cols[0]].sum()
                        label = f"{numeric_cols[0]}总和"
            elif agg_func == '平均值':
                if 'avg_value' in data.columns:
                    # 对于平均值，需要重新计算加权平均
                    if 'count_value' in data.columns:
                        total_sum = (data['avg_value'] * data['count_value']).sum()
                        total_count = data['count_value'].sum()
                        value = total_sum / total_count if total_count > 0 else 0
                    else:
                        value = data['avg_value'].mean()
                    label = "平均值"
                else:
                    numeric_cols = data.select_dtypes(include=['number']).columns
                    if len(numeric_cols) > 0:
                        value = data[numeric_cols[0]].mean()
                        label = f"{numeric_cols[0]}平均值"
            elif agg_func == '最大值':
                if 'max_value' in data.columns:
                    value = data['max_value'].max()
                    label = "最大值"
                else:
                    numeric_cols = data.select_dtypes(include=['number']).columns
                    if len(numeric_cols) > 0:
                        value = data[numeric_cols[0]].max()
                        label = f"{numeric_cols[0]}最大值"
            elif agg_func == '最小值':
                if 'min_value' in data.columns:
                    value = data['min_value'].min()
                    label = "最小值"
                else:
                    numeric_cols = data.select_dtypes(include=['number']).columns
                    if len(numeric_cols) > 0:
                        value = data[numeric_cols[0]].min()
                        label = f"{numeric_cols[0]}最小值"
            else:
                # 默认情况：显示第一行第一列的数据
                value = data.iloc[0, 0] if len(data.columns) > 0 else 0
                label = data.columns[0] if len(data.columns) > 0 else "数值"

            # 格式化数值显示
            if isinstance(value, float):
                if value.is_integer():
                    value = int(value)
                else:
                    value = round(value, 2)

            self.logger.info(f"📊 数字卡显示: 统计方式={agg_func}, 数值={value}, 标签={label}")

            # 🔧 新增：获取主题色配置
            theme_colors = self.get_theme_colors(config)

            # 🔧 修复：支持主题色配置的背景色
            if theme_colors and len(theme_colors) >= 2:
                # 使用主题色创建渐变背景
                bg_color = f"linear-gradient(135deg, {theme_colors[0]} 0%, {theme_colors[1]} 100%)"
            else:
                # 使用默认渐变背景
                bg_color = "linear-gradient(135deg, #667eea 0%, #764ba2 100%)"

            # 🔧 新增：获取用户配置的数据标签字体大小和颜色
            data_label_font_size = config.get('data_label_font_size', 48)  # 数字卡的数值字体大小
            data_label_font_color = config.get('data_label_font_color', '默认')

            # 获取数值的颜色，如果是默认则使用白色
            if data_label_font_color == '默认':
                value_color = 'white'
            else:
                value_color = self.color_map.get(data_label_font_color, 'white')

            # 🔧 修改：只有当用户输入了标签说明时才显示标签，否则不显示任何标签
            label_description = config.get('label_description', '').strip()
            display_label = label_description if label_description else None

            # 🔧 新增：数字卡单位配置
            show_unit = config.get('number_card_show_unit', False)
            unit_text = config.get('number_card_unit', '').strip()

            # 如果显示单位且有单位文本，则在数值后添加单位
            if show_unit and unit_text:
                value_display = f"{value} {unit_text}"
            else:
                value_display = str(value)

            # 创建数字卡片HTML
            html = f"""
            <div style="width: {width}px; height: {height}px; display: flex; flex-direction: column;
                        justify-content: center; align-items: center;
                        background: {bg_color};
                        color: white; border-radius: 10px; box-shadow: 0 4px 15px rgba(0,0,0,0.2);">"""

            # 🔧 新增：只有当标题不为空时才显示标题
            if title and title.strip():
                # 🔧 修复：使用用户配置的标题样式
                title_font_size = config.get('title_font_size', 16)
                title_font_bold = config.get('title_font_bold', True)
                title_color = config.get('title_color', '默认')
                title_position = config.get('title_position', '居中')

                # 获取标题颜色
                if title_color == '默认':
                    title_color_value = 'white'
                else:
                    title_color_value = self.color_map.get(title_color, 'white')

                # 设置字体粗细
                font_weight = 'bold' if title_font_bold else 'normal'

                # 设置文本对齐方式
                text_align_map = {
                    '居中': 'center',
                    '左对齐': 'left',
                    '右对齐': 'right'
                }
                text_align = text_align_map.get(title_position, 'center')

                html += f"""
                <h2 style="margin: 0; font-size: {title_font_size}px; font-weight: {font_weight}; color: {title_color_value}; text-align: {text_align};">{title}</h2>"""

            # 数值部分
            html += f"""
                <div style="font-size: {data_label_font_size}px; font-weight: bold; margin: 10px 0; color: {value_color};">{value_display}</div>"""

            # 🔧 新增：只有当有标签说明时才显示标签
            if display_label:
                # 🔧 新增：获取标签说明的样式配置
                label_description_font_size = config.get('label_description_font_size', 6)
                label_description_font_color = config.get('label_description_font_color', '默认')

                # 获取标签说明的颜色，如果是默认则使用白色并设置透明度
                if label_description_font_color == '默认':
                    label_color_style = 'color: white; opacity: 0.8;'
                else:
                    label_color = self.color_map.get(label_description_font_color, 'white')
                    label_color_style = f'color: {label_color};'

                html += f"""
                <div style="font-size: {label_description_font_size}px; {label_color_style}">{display_label}</div>"""

            html += """
            </div>
            """

            return html

        except Exception as e:
            self.logger.error(f"渲染数字卡失败: {e}")
            return self._get_error_html(str(e))

    def _render_table_chart(self, config: Dict[str, Any], data: pd.DataFrame) -> str:
        """渲染表格图表"""
        try:
            # 获取配置
            title = config.get('title', '数据表格')
            width = config.get('width', 800)
            height = config.get('height', 600)

            # 处理数据
            if data.empty:
                return self._get_no_data_html()

            # 确保数据不为空且有列
            if len(data.columns) == 0:
                return self._get_no_data_html()

            self.logger.info(f"📊 表格渲染 - 数据形状: {data.shape}, 列: {list(data.columns)}")

            # 获取表格样式配置
            table_styles = self._get_table_styles(config)

            # 获取表格列配置
            table_columns = config.get('table_columns', [])

            # 开始构建HTML
            html = f"""
            <div style="width: {width}px; height: {height}px; overflow: auto; font-family: Arial, sans-serif;">
                <style>
                    .data-table {{
                        width: 100%;
                        border-collapse: collapse;
                        margin: 0;
                        font-size: {table_styles['cell_font_size']}px;
                        background-color: {table_styles['cell_bg_color']};
                    }}
                    .data-table th {{
                        background-color: {table_styles['header_bg_color']};
                        color: {table_styles['header_font_color']};
                        font-size: {table_styles['header_font_size']}px;
                        font-weight: bold;
                        padding: 8px 12px;
                        text-align: left;
                        border: {table_styles['border_width']}px solid {table_styles['border_color']};
                    }}
                    .data-table td {{
                        padding: 6px 12px;
                        border: {table_styles['border_width']}px solid {table_styles['border_color']};
                        color: {table_styles['cell_font_color']};
                        background-color: {table_styles['cell_bg_color']};
                    }}
                    .data-table tr {{
                        background-color: {table_styles['cell_bg_color']};
                    }}
            """

            # 🔧 修复：添加斑马纹样式（仅在启用时）
            if table_styles['stripe']:
                html += f"""
                    .data-table tr:nth-child(even) {{
                        background-color: {table_styles['stripe_color']} !important;
                    }}
                    .data-table tr:nth-child(even) td {{
                        background-color: {table_styles['stripe_color']} !important;
                    }}
                """
            else:
                # 🔧 修复：当斑马纹禁用时，确保所有行都是统一背景色
                html += f"""
                    .data-table tr {{
                        background-color: {table_styles['cell_bg_color']} !important;
                    }}
                    .data-table tr td {{
                        background-color: {table_styles['cell_bg_color']} !important;
                    }}
                """

            # 添加悬停效果
            if table_styles['hover']:
                html += """
                    .data-table tr:hover {
                        background-color: rgba(0, 123, 255, 0.1);
                    }
                """

            # 添加排序功能的JavaScript
            html += """
                    .sortable {
                        cursor: pointer;
                        user-select: none;
                    }
                    .sortable:hover {
                        background-color: rgba(0, 123, 255, 0.1);
                    }
                    .sort-arrow {
                        margin-left: 5px;
                        font-size: 10px;
                        color: #999;
                    }
                </style>

                <script>
                function sortTable(columnIndex, tableId) {
                    const table = document.getElementById(tableId);
                    const tbody = table.querySelector('tbody');
                    const rows = Array.from(tbody.querySelectorAll('tr'));

                    // 获取当前排序状态
                    const header = table.querySelector(`th:nth-child(${columnIndex + 1})`);
                    const currentSort = header.getAttribute('data-sort') || 'none';

                    // 清除所有排序标记
                    table.querySelectorAll('th').forEach(th => {
                        th.setAttribute('data-sort', 'none');
                        const arrow = th.querySelector('.sort-arrow');
                        if (arrow) arrow.textContent = '↕';
                    });

                    // 确定新的排序方向
                    let newSort = 'asc';
                    if (currentSort === 'asc') newSort = 'desc';

                    // 排序行
                    rows.sort((a, b) => {
                        const aVal = a.cells[columnIndex].textContent.trim();
                        const bVal = b.cells[columnIndex].textContent.trim();

                        // 尝试数字比较
                        const aNum = parseFloat(aVal.replace(/[^0-9.-]/g, ''));
                        const bNum = parseFloat(bVal.replace(/[^0-9.-]/g, ''));

                        if (!isNaN(aNum) && !isNaN(bNum)) {
                            return newSort === 'asc' ? aNum - bNum : bNum - aNum;
                        } else {
                            return newSort === 'asc' ? aVal.localeCompare(bVal) : bVal.localeCompare(aVal);
                        }
                    });

                    // 重新插入排序后的行
                    rows.forEach(row => tbody.appendChild(row));

                    // 更新排序标记
                    header.setAttribute('data-sort', newSort);
                    const arrow = header.querySelector('.sort-arrow');
                    if (arrow) arrow.textContent = newSort === 'asc' ? '↑' : '↓';
                }

                function filterTable(input, tableId) {
                    const filter = input.value.toLowerCase();
                    const table = document.getElementById(tableId);
                    const rows = table.querySelectorAll('tbody tr');

                    rows.forEach(row => {
                        const text = row.textContent.toLowerCase();
                        row.style.display = text.includes(filter) ? '' : 'none';
                    });
                }
                </script>
            """

            # 生成表格ID
            table_id = f"data-table-{hash(str(config)) % 10000}"

            # 添加标题
            if title:
                title_color = self.color_map.get(config.get('title_color', '默认'), '#333')
                title_font_size = config.get('title_font_size', 16)
                html += f"""
                <div style="text-align: center; margin-bottom: 15px; font-size: {title_font_size}px; font-weight: bold; color: {title_color};">
                    {title}
                </div>
                """

            # 添加筛选输入框
            html += f"""
            <div style="margin-bottom: 10px; text-align: right;">
                <input type="text" placeholder="搜索表格内容..."
                       style="padding: 5px 10px; border: 1px solid #ddd; border-radius: 4px; width: 200px;"
                       oninput="filterTable(this, '{table_id}')">
            </div>
            """

            # 开始表格
            html += f'<table class="data-table" id="{table_id}">'

            # 表头
            html += '<thead><tr>'

            # 是否显示行号
            show_index = config.get('table_show_index', True)
            if show_index:
                html += '<th style="width: 60px;">#</th>'

            # 添加列标题
            for col_idx, col in enumerate(data.columns):
                # 查找列配置以获取显示名称和排序设置
                col_name = col
                sortable = True
                for col_config in table_columns:
                    if col_config.get('field') == col:
                        col_name = col_config.get('name', col)
                        sortable = col_config.get('sortable', True)
                        break

                # 计算实际列索引（考虑行号列）
                actual_col_idx = col_idx + (1 if show_index else 0)

                if sortable:
                    html += f'''<th class="sortable" data-sort="none" onclick="sortTable({actual_col_idx}, '{table_id}')">
                        {col_name}<span class="sort-arrow">↕</span>
                    </th>'''
                else:
                    html += f'<th>{col_name}</th>'

            html += '</tr></thead>'

            # 表体
            html += '<tbody>'

            # 限制显示行数
            max_rows = config.get('table_max_rows', 100)
            display_data = data.head(max_rows)

            for idx, (_, row) in enumerate(display_data.iterrows(), 1):
                html += '<tr>'

                # 行号
                if show_index:
                    html += f'<td style="text-align: center; font-weight: bold; color: #666;">{idx}</td>'

                # 数据列
                for col in data.columns:
                    value = row[col]

                    # 查找列配置以应用格式化
                    formatted_value = self._format_table_cell_value(value, col, table_columns)

                    # 获取列对齐方式
                    align = self._get_column_align(col, table_columns)

                    html += f'<td style="text-align: {align};">{formatted_value}</td>'

                html += '</tr>'

            html += '</tbody></table>'

            # 如果数据被截断，显示提示
            if len(data) > max_rows:
                html += f"""
                <div style="text-align: center; margin-top: 10px; color: #666; font-size: 12px;">
                    显示前 {max_rows} 行，共 {len(data)} 行数据
                </div>
                """

            html += '</div>'

            self.logger.info(f"📊 表格渲染完成，HTML长度: {len(html)}")
            return html

        except Exception as e:
            self.logger.error(f"渲染表格失败: {e}")
            return self._get_error_html(str(e))

    def _get_table_styles(self, config: Dict[str, Any]) -> Dict[str, str]:
        """获取表格样式配置"""
        # 颜色映射
        color_map = {
            '默认': '#333333',
            '黑色': '#000000',
            '白色': '#ffffff',
            '灰色': '#666666',
            '深灰': '#444444',
            '浅灰': '#f5f5f5',
            '蓝色': '#007bff',
            '浅蓝': '#e3f2fd',
            '绿色': '#28a745',
            '浅绿': '#e8f5e8',
            '红色': '#dc3545',
            '橙色': '#fd7e14',
            '紫色': '#6f42c1',
            '浅紫': '#f3e5f5',
            '浅黄': '#fff3cd'
        }

        bg_color_map = {
            '默认': '#ffffff',
            '白色': '#ffffff',
            '浅灰': '#f8f9fa',
            '蓝色': '#007bff',
            '绿色': '#28a745',
            '橙色': '#fd7e14',
            '紫色': '#6f42c1'
        }

        # 🔧 修复：获取主题色配置并应用到表格样式
        theme_colors = self.get_theme_colors(config)

        # 如果有主题色配置，使用主题色作为表头背景色
        header_bg_color = bg_color_map.get(config.get('table_header_bg_color', '默认'), '#f8f9fa')
        if theme_colors and len(theme_colors) > 0 and config.get('table_header_bg_color', '默认') == '默认':
            # 使用主题色的第一个颜色作为表头背景色（淡化处理）
            primary_color = theme_colors[0]
            # 将主题色转换为淡化版本用作表头背景
            header_bg_color = self._lighten_color(primary_color, 0.9)

        # 如果有主题色配置，使用主题色作为边框颜色
        border_color = color_map.get(config.get('table_border_color', '默认'), '#dee2e6')
        if theme_colors and len(theme_colors) > 0 and config.get('table_border_color', '默认') == '默认':
            # 使用主题色的第一个颜色作为边框色（淡化处理）
            border_color = self._lighten_color(theme_colors[0], 0.7)

        return {
            'border_width': config.get('table_border_width', 1),
            # 🔧 修复：使用计算出的主题色边框颜色
            'border_color': border_color,
            # 🔧 修复：使用计算出的主题色表头背景颜色
            'header_bg_color': header_bg_color,
            'header_font_color': color_map.get(config.get('table_header_font_color', '默认'), '#333333'),
            'header_font_size': config.get('table_header_font_size', 14),
            'cell_font_size': config.get('table_cell_font_size', 12),
            'cell_font_color': color_map.get(config.get('table_cell_font_color', '默认'), '#333333'),
            # 🔧 修复：单元格背景色默认使用白色，而不是color_map中的默认值
            'cell_bg_color': bg_color_map.get(config.get('table_cell_bg_color', '默认'), '#ffffff'),
            # 🔧 修复：将表格默认斑马纹样式改为False，确保默认为全白色背景
            'stripe': config.get('table_stripe', False),
            # 🔧 修复：斑马纹颜色也使用bg_color_map，确保颜色一致性
            'stripe_color': bg_color_map.get(config.get('table_stripe_color', '浅灰'), '#f8f9fa'),
            'hover': config.get('table_hover', True)
        }

    def _lighten_color(self, hex_color: str, factor: float) -> str:
        """
        淡化颜色

        Args:
            hex_color: 十六进制颜色值，如 '#5470C6'
            factor: 淡化因子，0-1之间，越接近1越淡

        Returns:
            淡化后的十六进制颜色值
        """
        try:
            # 移除 # 符号
            hex_color = hex_color.lstrip('#')

            # 转换为RGB
            r = int(hex_color[0:2], 16)
            g = int(hex_color[2:4], 16)
            b = int(hex_color[4:6], 16)

            # 向白色(255,255,255)淡化
            r = int(r + (255 - r) * factor)
            g = int(g + (255 - g) * factor)
            b = int(b + (255 - b) * factor)

            # 确保值在0-255范围内
            r = max(0, min(255, r))
            g = max(0, min(255, g))
            b = max(0, min(255, b))

            # 转换回十六进制
            return f'#{r:02x}{g:02x}{b:02x}'

        except Exception as e:
            self.logger.warning(f"颜色淡化失败: {e}, 使用原色: {hex_color}")
            return hex_color

    def _format_table_cell_value(self, value, column: str, table_columns: List[Dict[str, Any]]) -> str:
        """格式化表格单元格的值"""
        try:
            # 查找列配置
            col_config = None
            for config in table_columns:
                if config.get('field') == column:
                    col_config = config
                    break

            if not col_config:
                # 没有配置，使用默认格式
                return self._default_format_value(value)

            format_type = col_config.get('format', '默认')
            custom_format = col_config.get('custom_format', '')

            # 处理空值
            if pd.isna(value) or value is None or value == '':
                return '-'

            # 根据格式类型处理
            if format_type == '默认':
                return self._default_format_value(value)
            elif format_type == '整数':
                try:
                    return str(int(float(value)))
                except:
                    return str(value)
            elif format_type == '小数(1位)':
                try:
                    return f"{float(value):.1f}"
                except:
                    return str(value)
            elif format_type == '小数(2位)':
                try:
                    return f"{float(value):.2f}"
                except:
                    return str(value)
            elif format_type == '百分比':
                try:
                    return f"{float(value):.1f}%"
                except:
                    return str(value)
            elif format_type == '货币':
                try:
                    return f"¥{float(value):,.2f}"
                except:
                    return str(value)
            elif format_type == '自定义' and custom_format:
                try:
                    return custom_format.format(value)
                except:
                    return str(value)
            else:
                return self._default_format_value(value)

        except Exception as e:
            self.logger.error(f"格式化单元格值失败: {e}")
            return str(value)

    def _default_format_value(self, value) -> str:
        """默认值格式化"""
        if pd.isna(value) or value is None:
            return '-'

        # 如果是数字，尝试格式化
        try:
            num_value = float(value)
            if num_value.is_integer():
                return str(int(num_value))
            else:
                return f"{num_value:.2f}"
        except:
            return str(value)

    def _get_column_align(self, column: str, table_columns: List[Dict[str, Any]]) -> str:
        """获取列的对齐方式"""
        # 查找列配置
        for config in table_columns:
            if config.get('field') == column:
                align = config.get('align', '左对齐')
                align_map = {
                    '左对齐': 'left',
                    '居中': 'center',
                    '右对齐': 'right'
                }
                return align_map.get(align, 'left')

        # 默认对齐方式：数字右对齐，其他左对齐
        return 'left'

    def _fix_chart_div_id(self, html: str, chart_config: Dict[str, Any]) -> str:
        """
        修复图表HTML中的div ID，确保与chart_id一致

        参数:
            html: 原始图表HTML
            chart_config: 图表配置

        返回:
            修复ID后的HTML
        """
        try:
            import re

            # 获取期望的图表ID
            chart_id = chart_config.get('chart_id', f"chart_{id(chart_config)}")

            # 查找图表div的ID模式
            div_pattern = r'<div\s+id="([^"]+)"\s+[^>]*style="[^"]*width:\s*\d+px[^"]*height:\s*\d+px[^"]*"[^>]*>'
            match = re.search(div_pattern, html)

            if match:
                original_id = match.group(1)
                # 注释图表类信息通知
                # print(f"🔧 [图表ID修复] 原始ID: {original_id} -> 目标ID: {chart_id}")

                # 替换div ID
                html = html.replace(f'id="{original_id}"', f'id="{chart_id}"')

                # 同时替换JavaScript中可能引用的ID
                html = html.replace(f"'{original_id}'", f"'{chart_id}'")
                html = html.replace(f'"{original_id}"', f'"{chart_id}"')

                # 注释图表类信息通知
                # print(f"✅ [图表ID修复] 已将图表ID从 {original_id} 修复为 {chart_id}")
            else:
                # 注释图表类信息通知
                # print(f"⚠️ [图表ID修复] 未找到图表div，使用默认ID: {chart_id}")
                pass

            return html

        except Exception as e:
            self.logger.error(f"修复图表div ID失败: {e}")
            return html

    def _inject_chart_interaction_js(self, html: str, chart_config: Dict[str, Any]) -> str:
        """
        向图表HTML中注入交互JavaScript代码

        参数:
            html: 原始图表HTML
            chart_config: 图表配置

        返回:
            注入交互代码后的HTML
        """
        try:
            # 获取图表ID和类型
            chart_id = chart_config.get('chart_id', f"chart_{id(chart_config)}")
            chart_type = chart_config.get('chart_type', 'bar')

            # 生成交互JavaScript代码
            # 注释图表类信息通知
            # print(f"🔧 [图表渲染] 开始生成交互JS: {chart_id} ({chart_type})")
            interaction_js = self._generate_chart_interaction_js(chart_id, chart_type, chart_config)
            # 注释图表类信息通知
            # print(f"📝 [图表渲染] 交互JS长度: {len(interaction_js)} 字符")

            # 查找合适的注入位置（在</script>标签之前）
            if '</script>' in html:
                # 在最后一个</script>标签之前注入
                last_script_end = html.rfind('</script>')
                if last_script_end != -1:
                    html = html[:last_script_end] + interaction_js + '\n' + html[last_script_end:]
                    # 注释图表类信息通知
                    # self.logger.info(f"✅ 已为图表 {chart_id} 注入交互JavaScript代码")
                    # 注释图表类信息通知
                    # print(f"✅ [图表渲染] 交互JS已注入: {chart_id}")
            else:
                # 如果没有找到</script>标签，在</body>之前注入
                if '</body>' in html:
                    html = html.replace('</body>', f'<script>{interaction_js}</script>\n</body>')
                else:
                    # 如果都没有，在HTML末尾添加
                    html += f'<script>{interaction_js}</script>'
                # 注释图表类信息通知
                # self.logger.info(f"✅ 已为图表 {chart_id} 在HTML末尾注入交互JavaScript代码")
                # 注释图表类信息通知
                # print(f"✅ [图表渲染] 交互JS已注入到HTML末尾: {chart_id}")

            return html

        except Exception as e:
            self.logger.error(f"注入图表交互JavaScript代码失败: {e}")
            return html

    def _generate_chart_interaction_js(self, chart_id: str, chart_type: str, chart_config: Dict[str, Any]) -> str:
        """
        生成图表交互JavaScript代码

        参数:
            chart_id: 图表ID
            chart_type: 图表类型
            chart_config: 图表配置

        返回:
            JavaScript代码字符串
        """
        try:
            # 获取交互配置
            enable_interaction = chart_config.get('enable_interaction', True)
            if not enable_interaction:
                return ""

            # 获取目标表格和字段映射
            target_table = chart_config.get('target_table', '试验问题表')
            field_mapping = chart_config.get('field_mapping', {})

            # 如果没有配置字段映射，使用默认映射
            if not field_mapping:
                field_mapping = self._get_default_field_mapping(chart_type, chart_config)

            js_code = f"""
            // 图表交互代码 - {chart_id}
            (function() {{
                console.log('🚀 [图表交互] 初始化图表交互: {chart_id}');

                // 等待图表对象就绪
                var waitForChart = function() {{
                    var chartInstance = null;
                    console.log('🔍 [图表交互] 等待图表就绪: {chart_id}');

                    // 查找图表实例
                    if (typeof echarts !== 'undefined') {{
                        console.log('✅ [图表交互] ECharts已加载');

                        // 🔧 方法1：通过特定的图表ID查找对应的DOM元素
                        var targetChartDiv = document.getElementById('{chart_id}');
                        if (targetChartDiv) {{
                            chartInstance = echarts.getInstanceByDom(targetChartDiv);
                            if (chartInstance) {{
                                console.log('✅ [图表交互] 通过ID找到目标图表实例: {chart_id}');
                            }}
                        }}

                        // 🔧 方法2：如果没有找到，尝试通过包含图表ID的div查找
                        if (!chartInstance) {{
                            var chartDivs = document.querySelectorAll('div[id*="{chart_id}"], div[_echarts_instance_]');
                            console.log('🔍 [图表交互] 通过ID模糊匹配发现图表容器数量:', chartDivs.length);

                            for (var i = 0; i < chartDivs.length; i++) {{
                                var instance = echarts.getInstanceByDom(chartDivs[i]);
                                if (instance) {{
                                    console.log('✅ [图表交互] 找到匹配的图表实例:', i, chartDivs[i].id, instance);
                                    chartInstance = instance;
                                    break;
                                }}
                            }}
                        }}

                        // 🔧 方法3：通过全局变量查找
                        if (!chartInstance && window.chart_{chart_id}) {{
                            console.log('✅ [图表交互] 通过全局变量找到图表实例');
                            chartInstance = window.chart_{chart_id};
                        }}

                        // 🔧 方法4：最后的备用方案，查找最新创建的图表实例
                        if (!chartInstance) {{
                            var allChartDivs = document.querySelectorAll('div[_echarts_instance_]');
                            console.log('🔍 [图表交互] 备用方案：发现所有图表容器数量:', allChartDivs.length);

                            // 查找最后一个图表实例（通常是最新创建的）
                            for (var i = allChartDivs.length - 1; i >= 0; i--) {{
                                var instance = echarts.getInstanceByDom(allChartDivs[i]);
                                if (instance) {{
                                    console.log('⚠️ [图表交互] 使用备用方案找到图表实例:', i, allChartDivs[i].id, instance);
                                    chartInstance = instance;
                                    break;
                                }}
                            }}
                        }}
                    }} else {{
                        console.log('⚠️ [图表交互] ECharts未加载，继续等待...');
                    }}

                    if (chartInstance) {{
                        console.log('🎉 [图表交互] 图表对象就绪: {chart_id}');
                        setupChartInteraction(chartInstance);
                    }} else {{
                        console.log('⏳ [图表交互] 图表未就绪，100ms后重试: {chart_id}');
                        setTimeout(waitForChart, 100);
                    }}
                }};

                var setupChartInteraction = function(chartInstance) {{
                    try {{
                        console.log('🔧 [图表交互] 开始设置图表点击监听: {chart_id}');

                        // 添加点击事件监听
                        chartInstance.on('click', function(params) {{
                            console.log('🎯 [图表交互] 图表点击事件触发!', params);
                            console.log('🎯 [图表交互] 点击的图表ID: {chart_id}');
                            console.log('🎯 [图表交互] 点击的元素名称:', params.name);
                            console.log('🎯 [图表交互] 点击的数值:', params.value);

                            // 构建点击数据
                            var clickData = {{
                                chartId: '{chart_id}',
                                chartType: '{chart_type}',
                                targetTable: '{target_table}',
                                name: params.name,
                                value: params.value,
                                data: params.data,
                                seriesName: params.seriesName,
                                dataIndex: params.dataIndex,
                                seriesIndex: params.seriesIndex,
                                fieldMapping: {self._js_encode_dict(field_mapping)}
                            }};

                            // 构建筛选条件
                            var filterConditions = {{}};
                            var fieldMapping = clickData.fieldMapping;
                            console.log('🔧 [图表交互] 字段映射配置:', fieldMapping);

                            // 根据字段映射构建筛选条件
                            for (var chartField in fieldMapping) {{
                                var tableField = fieldMapping[chartField];
                                var value = null;

                                if (chartField === 'name' && params.name) {{
                                    value = params.name;
                                }} else if (chartField === 'value' && params.value !== undefined) {{
                                    value = params.value;
                                }} else if (chartField === 'seriesName' && params.seriesName) {{
                                    value = params.seriesName;
                                }} else if (params.data && typeof params.data === 'object' && params.data[chartField]) {{
                                    value = params.data[chartField];
                                }}

                                if (value !== null && value !== undefined) {{
                                    filterConditions[tableField] = value;
                                    console.log('🔧 [图表交互] 添加筛选条件:', tableField, '=', value);
                                }}
                            }}

                            clickData.filterConditions = filterConditions;
                            console.log('📊 [图表交互] 完整点击数据:', clickData);

                            // 保存到window对象供Python端读取
                            window.lastChartClickData = clickData;
                            console.log('✅ [图表交互] 点击数据已保存到window.lastChartClickData');

                            // 同时保存到localStorage作为备用方案
                            try {{
                                // 检查localStorage是否可用
                                if (typeof Storage !== 'undefined' && typeof localStorage !== 'undefined') {{
                                    localStorage.setItem('chart_click_data', JSON.stringify(clickData));
                                    console.log('✅ [图表交互] 点击数据已保存到localStorage');
                                }}
                            }} catch (e) {{
                                console.log('⚠️ [图表交互] localStorage访问失败:', e);
                            }}

                            // 尝试通过Qt桥接发送数据
                            if (typeof qtBridge !== 'undefined' && qtBridge.handleChartClick) {{
                                console.log('🔗 [图表交互] 使用qtBridge发送数据');
                                qtBridge.handleChartClick(JSON.stringify(clickData));
                            }} else if (typeof chartNavigationBridge !== 'undefined' && chartNavigationBridge.handleChartClick) {{
                                console.log('🔗 [图表交互] 使用chartNavigationBridge发送数据');
                                // 使用新的导航桥接
                                var navigationData = {{
                                    chartId: '{chart_id}',
                                    chartType: '{chart_type}',
                                    clickedElement: params.name || '',
                                    clickedValue: params.value || '',
                                    seriesName: params.seriesName || '',
                                    dataIndex: params.dataIndex || 0,
                                    rawData: {{
                                        data: params.data,
                                        color: params.color,
                                        seriesIndex: params.seriesIndex,
                                        componentType: params.componentType
                                    }}
                                }};
                                chartNavigationBridge.handleChartClick(JSON.stringify(navigationData));
                            }} else {{
                                console.log('⚠️ [图表交互] 没有可用的Qt桥接，依赖Python轮询检测');
                            }}

                            // 显示用户反馈
                            if (filterConditions && Object.keys(filterConditions).length > 0) {{
                                console.log('🚀 [图表交互] 将跳转到表格:', clickData.targetTable, '筛选条件:', filterConditions);
                                // 🔧 添加视觉反馈
                                if (params.event && params.event.target) {{
                                    var originalColor = params.event.target.style.opacity;
                                    params.event.target.style.opacity = '0.7';
                                    setTimeout(function() {{
                                        params.event.target.style.opacity = originalColor;
                                    }}, 200);
                                }}
                            }} else {{
                                console.log('⚠️ [图表交互] 没有有效的筛选条件');
                            }}
                        }});

                        console.log('✅ [图表交互] 图表交互设置完成: {chart_id}');
                    }} catch (e) {{
                        console.error('❌ [图表交互] 设置图表交互失败:', e);
                    }}
                }};

                // 开始等待图表就绪
                setTimeout(waitForChart, 100);
            }})();
            """

            return js_code

        except Exception as e:
            self.logger.error(f"生成图表交互JavaScript代码失败: {e}")
            return ""

    def _get_default_field_mapping(self, chart_type: str, chart_config: Dict[str, Any]) -> Dict[str, str]:
        """
        获取默认的字段映射配置

        参数:
            chart_type: 图表类型
            chart_config: 图表配置

        返回:
            字段映射字典
        """
        try:
            # 根据图表类型和配置推断字段映射
            field_mapping = {}

            if chart_type == 'bar':
                # 柱状图：通常点击的是分类名称
                x_field = chart_config.get('x_field', '')
                if x_field:
                    field_mapping['name'] = x_field

            elif chart_type == 'pie':
                # 饼图：通常点击的是分类名称
                field_mapping['name'] = chart_config.get('category_field', '类别')

            elif chart_type == 'line':
                # 折线图：可能是时间或分类
                x_field = chart_config.get('x_field', '')
                if x_field:
                    field_mapping['name'] = x_field

            # 如果没有配置，使用通用映射
            if not field_mapping:
                field_mapping = {'name': '类别'}

            self.logger.info(f"为图表类型 {chart_type} 生成默认字段映射: {field_mapping}")
            return field_mapping

        except Exception as e:
            self.logger.error(f"获取默认字段映射失败: {e}")
            return {'name': '类别'}

    def _js_encode_dict(self, data: Dict[str, Any]) -> str:
        """
        将Python字典编码为JavaScript对象字符串

        参数:
            data: Python字典

        返回:
            JavaScript对象字符串
        """
        try:
            import json
            return json.dumps(data)
        except Exception as e:
            self.logger.error(f"编码JavaScript字典失败: {e}")
            return "{}"

