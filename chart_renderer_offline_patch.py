#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
图表渲染器离线修复补丁

该补丁修复现有图表渲染器的离线问题，确保真正使用嵌入式资源。

问题：
1. 现有的chart_renderer.py虽然声称支持嵌入式资源，但实际仍使用PyEcharts标准流程
2. bar.render_embed()等方法仍会生成包含外部资源引用的HTML
3. 嵌入式资源模块存在但从未被实际调用

解决方案：
1. 修改_render_bar_chart等方法，使用真正的嵌入式HTML生成
2. 完全绕过PyEcharts的render_embed()方法
3. 直接生成包含嵌入式JavaScript的HTML

使用方法：
1. 备份原始的chart_renderer.py
2. 运行此补丁脚本
3. 重启应用程序

作者: AI Assistant
创建时间: 2025-08-05
"""

import os
import sys
import json
import shutil
import logging
from pathlib import Path
from typing import Dict, Any

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

logger = logging.getLogger(__name__)


class ChartRendererPatcher:
    """图表渲染器补丁器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.chart_renderer_path = project_root / "src" / "utils" / "chart_renderer.py"
        self.backup_path = project_root / "src" / "utils" / "chart_renderer_backup.py"
        
    def create_backup(self) -> bool:
        """创建原始文件备份"""
        try:
            if self.chart_renderer_path.exists():
                shutil.copy2(self.chart_renderer_path, self.backup_path)
                self.logger.info(f"✅ 已创建备份: {self.backup_path}")
                return True
            else:
                self.logger.error(f"原始文件不存在: {self.chart_renderer_path}")
                return False
        except Exception as e:
            self.logger.error(f"创建备份失败: {e}")
            return False
    
    def generate_offline_bar_chart_method(self) -> str:
        """生成离线柱状图渲染方法"""
        return '''
    def _render_bar_chart_offline(self, config: Dict[str, Any], data: pd.DataFrame) -> str:
        """渲染柱状图 - 真正的离线版本"""
        try:
            # 检查嵌入式资源
            if not self.embedded_mode_enabled:
                self.logger.warning("嵌入式模式未启用，回退到标准方法")
                return self._render_bar_chart_standard(config, data)
            
            # 获取嵌入式JavaScript资源
            try:
                from .embedded_resources import get_embedded_chart_scripts
                embedded_scripts = get_embedded_chart_scripts()
                if not embedded_scripts or 'https://' in embedded_scripts:
                    self.logger.warning("嵌入式资源不可用，回退到标准方法")
                    return self._render_bar_chart_standard(config, data)
            except ImportError:
                self.logger.warning("无法导入嵌入式资源，回退到标准方法")
                return self._render_bar_chart_standard(config, data)
            
            # 提取配置
            title = config.get('title', '柱状图')
            width = config.get('width', 800)
            height = config.get('height', 600)
            x_field = config.get('x_field')
            y_field = config.get('y_field')
            
            # 处理数据
            if data.empty:
                return self._get_no_data_html()
            
            # 自动检测字段
            if not x_field and len(data.columns) > 0:
                x_field = data.columns[0]
            if not y_field and len(data.columns) > 1:
                y_field = data.columns[1]
            elif not y_field:
                y_field = x_field
            
            # 提取数据
            x_data = data[x_field].astype(str).tolist()
            y_data = data[y_field].fillna(0).astype(float).tolist()
            
            # 生成图表配置
            chart_option = {
                'title': {'text': title},
                'tooltip': {},
                'xAxis': {'data': x_data},
                'yAxis': {},
                'series': [{
                    'name': y_field,
                    'type': 'bar',
                    'data': y_data
                }]
            }
            
            # 生成完全离线的HTML
            chart_id = f"chart_{hash(str(config)) % 10000}"
            html = f"""
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{title}</title>
    <style>
        body {{
            margin: 0;
            padding: 0;
            background: white;
        }}
        #{chart_id} {{
            width: {width}px;
            height: {height}px;
            margin: 0 auto;
        }}
    </style>
</head>
<body>
    <div id="{chart_id}"></div>
    
    {embedded_scripts}
    
    <script type="text/javascript">
    // 初始化图表
    var chartDom = document.getElementById('{chart_id}');
    var myChart = echarts.init(chartDom);
    
    // 图表配置
    var option = {json.dumps(chart_option, ensure_ascii=False)};
    
    // 设置图表
    myChart.setOption(option);
    
    // 响应式调整
    window.addEventListener('resize', function() {{
        myChart.resize();
    }});
    </script>
</body>
</html>
"""
            
            self.logger.info(f"✅ 离线柱状图渲染成功: {len(html)} 字符")
            return html
            
        except Exception as e:
            self.logger.error(f"离线柱状图渲染失败: {e}")
            return self._get_error_html(str(e))
    
    def _render_bar_chart_standard(self, config: Dict[str, Any], data: pd.DataFrame) -> str:
        """标准柱状图渲染方法（原始方法的重命名版本）"""
        # 这里是原始的_render_bar_chart方法内容
        # 为了简化，这里返回错误HTML
        return self._get_error_html("标准渲染方法需要从原始代码复制")
'''
    
    def apply_patch(self) -> bool:
        """应用补丁"""
        try:
            # 1. 创建备份
            if not self.create_backup():
                return False
            
            # 2. 读取原始文件
            with open(self.chart_renderer_path, 'r', encoding='utf-8') as f:
                original_content = f.read()
            
            # 3. 查找_render_bar_chart方法的位置
            method_start = original_content.find('def _render_bar_chart(')
            if method_start == -1:
                self.logger.error("未找到_render_bar_chart方法")
                return False
            
            # 4. 找到方法结束位置（下一个同级方法）
            lines = original_content.split('\\n')
            start_line = 0
            for i, line in enumerate(lines):
                if 'def _render_bar_chart(' in line:
                    start_line = i
                    break
            
            # 找到方法结束
            end_line = len(lines)
            for i in range(start_line + 1, len(lines)):
                line = lines[i]
                if line.strip() and not line.startswith(' ') and not line.startswith('\\t'):
                    if line.startswith('def ') or line.startswith('class '):
                        end_line = i
                        break
            
            # 5. 替换方法
            new_lines = lines[:start_line]
            
            # 添加新的离线方法
            new_lines.append('    def _render_bar_chart(self, config: Dict[str, Any], data: pd.DataFrame) -> str:')
            new_lines.append('        """渲染柱状图 - 优先使用离线模式"""')
            new_lines.append('        if self.embedded_mode_enabled:')
            new_lines.append('            return self._render_bar_chart_offline(config, data)')
            new_lines.append('        else:')
            new_lines.append('            return self._render_bar_chart_standard(config, data)')
            new_lines.append('')
            
            # 添加离线渲染方法
            offline_method_lines = self.generate_offline_bar_chart_method().split('\\n')
            new_lines.extend(offline_method_lines)
            
            # 重命名原始方法
            original_method_lines = lines[start_line:end_line]
            for line in original_method_lines:
                if 'def _render_bar_chart(' in line:
                    line = line.replace('def _render_bar_chart(', 'def _render_bar_chart_standard(')
                new_lines.append(line)
            
            # 添加剩余内容
            new_lines.extend(lines[end_line:])
            
            # 6. 写入修改后的文件
            modified_content = '\\n'.join(new_lines)
            with open(self.chart_renderer_path, 'w', encoding='utf-8') as f:
                f.write(modified_content)
            
            self.logger.info("✅ 补丁应用成功")
            return True
            
        except Exception as e:
            self.logger.error(f"应用补丁失败: {e}")
            # 恢复备份
            if self.backup_path.exists():
                shutil.copy2(self.backup_path, self.chart_renderer_path)
                self.logger.info("已恢复原始文件")
            return False
    
    def restore_backup(self) -> bool:
        """恢复备份"""
        try:
            if self.backup_path.exists():
                shutil.copy2(self.backup_path, self.chart_renderer_path)
                self.logger.info("✅ 已恢复原始文件")
                return True
            else:
                self.logger.error("备份文件不存在")
                return False
        except Exception as e:
            self.logger.error(f"恢复备份失败: {e}")
            return False


def main():
    """主函数"""
    print("🔧 图表渲染器离线修复补丁")
    print("="*50)
    
    patcher = ChartRendererPatcher()
    
    # 询问用户操作
    print("请选择操作:")
    print("1. 应用离线修复补丁")
    print("2. 恢复原始文件")
    print("3. 退出")
    
    choice = input("请输入选择 (1-3): ").strip()
    
    if choice == '1':
        print("\\n正在应用离线修复补丁...")
        if patcher.apply_patch():
            print("✅ 补丁应用成功！")
            print("请重启应用程序以使修改生效。")
        else:
            print("❌ 补丁应用失败！")
    
    elif choice == '2':
        print("\\n正在恢复原始文件...")
        if patcher.restore_backup():
            print("✅ 原始文件已恢复！")
        else:
            print("❌ 恢复失败！")
    
    elif choice == '3':
        print("退出。")
    
    else:
        print("无效选择。")


if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    main()
